#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
import pytz
from ib_insync import IB, Future, util
import pandas as pd

# Pridaj PY script adresár do cesty
sys.path.append(os.path.join(os.path.dirname(__file__), 'PY script'))

try:
    import config
    import utils
except ImportError as e:
    print(f"Chyba pri importovaní modulov: {e}")
    sys.exit(1)

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(filename)s:%(lineno)d:%(name)s:%(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def debug_mgc_data():
    """Diagnostika MGC dát - kontrola kontraktu a historických dát"""
    
    ib = IB()
    
    try:
        logger.info("=== DIAGNOSTIKA MGC DÁT ===")
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=998)
        logger.info("Úspešne pripojený k IB")
        
        # 1. Získaj najlepší MGC kontrakt
        logger.info("\n--- 1. ZÍSKANIE NAJLEPŠIEHO MGC KONTRAKTU ---")
        mgc_contract = utils.get_best_contract_for_instrument(ib, 'MGC', 'COMEX', 'USD')
        
        if not mgc_contract:
            logger.error("Nepodarilo sa získať MGC kontrakt!")
            return
            
        logger.info(f"Vybraný MGC kontrakt: {mgc_contract.localSymbol}")
        logger.info(f"ConId: {mgc_contract.conId}")
        logger.info(f"LastTradeDateOrContractMonth: {mgc_contract.lastTradeDateOrContractMonth}")
        
        # 2. Získaj detaily kontraktu
        logger.info("\n--- 2. DETAILY KONTRAKTU ---")
        try:
            contract_details = ib.reqContractDetails(mgc_contract)
            if contract_details:
                detail = contract_details[0]
                logger.info(f"TimeZoneId: {detail.timeZoneId}")
                logger.info(f"TradingHours: {detail.tradingHours}")
                logger.info(f"LiquidHours: {detail.liquidHours}")
        except Exception as e:
            logger.error(f"Chyba pri získavaní detailov kontraktu: {e}")
        
        # 3. Získaj aktuálne market data
        logger.info("\n--- 3. AKTUÁLNE MARKET DATA ---")
        try:
            ticker = ib.reqMktData(mgc_contract, '', False, False)
            ib.sleep(2)
            
            logger.info(f"Last: {ticker.last}")
            logger.info(f"Bid: {ticker.bid}")
            logger.info(f"Ask: {ticker.ask}")
            logger.info(f"Close: {ticker.close}")
            
            ib.cancelMktData(mgc_contract)
        except Exception as e:
            logger.error(f"Chyba pri získavaní market data: {e}")
        
        # 4. Získaj denné historické dáta (presne ako v hlavnom skripte)
        logger.info("\n--- 4. DENNÉ HISTORICKÉ DÁTA ---")
        try:
            logger.info("Požadujem denné dáta s parametrami z hlavného skriptu:")
            logger.info("  durationStr='5 D', barSizeSetting='1 day', whatToShow='ASK', useRTH=False")
            
            daily_bars = ib.reqHistoricalData(
                mgc_contract, 
                '', 
                '5 D', 
                '1 day', 
                'ASK', 
                False, 
                formatDate=1
            )
            
            logger.info(f"Získal som {len(daily_bars)} denných sviečok:")
            
            for i, bar in enumerate(daily_bars):
                utc_time = bar.date.replace(tzinfo=timezone.utc)
                ny_time = utc_time.astimezone(pytz.timezone('America/New_York'))
                
                logger.info(f"  [{i}] {utc_time.strftime('%Y-%m-%d')} | "
                          f"NY: {ny_time.strftime('%Y-%m-%d')} | "
                          f"O={bar.open:.2f} H={bar.high:.2f} L={bar.low:.2f} C={bar.close:.2f} V={bar.volume}")
            
            # 5. Vypočítaj Camarilla pivoty z posledných dát
            if len(daily_bars) >= 2:
                logger.info("\n--- 5. VÝPOČET CAMARILLA PIVOTOV ---")
                
                # Konvertuj na DataFrame
                df = util.df(daily_bars)
                logger.info(f"DataFrame shape: {df.shape}")
                logger.info(f"DataFrame columns: {df.columns.tolist()}")
                logger.info(f"DataFrame index type: {type(df.index)}")
                
                # Použij presne tú istú logiku ako v utils.calc_pivots
                if len(df) >= 2:
                    prev_day_data = df.iloc[-2]  # Predchádzajúci deň
                    
                    logger.info(f"Používam dáta z predchádzajúceho dňa (iloc[-2]):")
                    logger.info(f"  Dátum: {prev_day_data.name}")
                    logger.info(f"  High: {prev_day_data['high']:.2f}")
                    logger.info(f"  Low: {prev_day_data['low']:.2f}")
                    logger.info(f"  Close: {prev_day_data['close']:.2f}")
                    
                    ph = float(prev_day_data['high'])
                    pl = float(prev_day_data['low'])
                    pc = float(prev_day_data['close'])
                    rng = ph - pl
                    
                    h4 = pc + rng * 0.55
                    l4 = pc - rng * 0.55
                    
                    logger.info(f"  Range: {rng:.2f}")
                    logger.info(f"  H4 (Camarilla): {h4:.2f}")
                    logger.info(f"  L4 (Camarilla): {l4:.2f}")
                    
                    # Porovnaj s aktuálnymi cenami
                    if daily_bars:
                        latest_bar = daily_bars[-1]
                        logger.info(f"\nPorovnanie s najnovšími dátami:")
                        logger.info(f"  Najnovší close: {latest_bar.close:.2f}")
                        logger.info(f"  Je close > H4? {latest_bar.close > h4}")
                        logger.info(f"  Je close < L4? {latest_bar.close < l4}")
                        
        except Exception as e:
            logger.error(f"Chyba pri získavaní denných dát: {e}")
        
        # 6. Získaj hodinové dáta pre porovnanie
        logger.info("\n--- 6. HODINOVÉ DÁTA PRE POROVNANIE ---")
        try:
            hourly_bars = ib.reqHistoricalData(
                mgc_contract,
                '',
                '2 D',
                '1 hour',
                'ASK',
                False,
                formatDate=1
            )
            
            logger.info(f"Získal som {len(hourly_bars)} hodinových sviečok")
            logger.info("Posledných 5 hodinových sviečok:")
            
            for bar in hourly_bars[-5:]:
                utc_time = bar.date.replace(tzinfo=timezone.utc)
                ny_time = utc_time.astimezone(pytz.timezone('America/New_York'))
                
                logger.info(f"  {utc_time.strftime('%Y-%m-%d %H:%M')} UTC | "
                          f"{ny_time.strftime('%Y-%m-%d %H:%M')} NY | "
                          f"O={bar.open:.2f} H={bar.high:.2f} L={bar.low:.2f} C={bar.close:.2f}")
                          
        except Exception as e:
            logger.error(f"Chyba pri získavaní hodinových dát: {e}")
            
        # 7. Kontrola online cien
        logger.info("\n--- 7. KONTROLA REÁLNYCH TRŽNÝCH CIEN ---")
        logger.info("Porovnajte získané ceny s:")
        logger.info("  - CME Group: https://www.cmegroup.com/markets/metals/precious/gold.html")
        logger.info("  - TradingView: https://www.tradingview.com/symbols/COMEX-GC1!/")
        logger.info("  - Yahoo Finance: https://finance.yahoo.com/quote/GC%3DF/")
        
        if daily_bars:
            latest_close = daily_bars[-1].close
            logger.info(f"\nAktuálny close z IB: {latest_close:.2f}")
            logger.info(f"Očakávaný rozsah pre zlato: 2300-2800 USD/oz")
            
            if latest_close < 2000 or latest_close > 3000:
                logger.warning("⚠️  POZOR: Cena je mimo očakávaného rozsahu pre zlato!")
            else:
                logger.info("✅ Cena je v rozumnom rozsahu pre zlato")
                
    except Exception as e:
        logger.error(f"Chyba v diagnostike: {e}", exc_info=True)
        
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == '__main__':
    debug_mgc_data()
