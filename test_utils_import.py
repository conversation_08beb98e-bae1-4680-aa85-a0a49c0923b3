#!/usr/bin/env python3
"""
Test importu novej funkcie z utils
"""

import sys
import os
import logging

# Pridaj cestu k modulom
sys.path.append('/root/PY')

# Nastavenie logovania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_import():
    try:
        logger.info("Testujem import utils modulu...")
        import utils
        
        logger.info("✓ utils modul importovaný úspešne")
        
        # Skontroluj či funkcia existuje
        if hasattr(utils, 'get_best_contract_for_instrument'):
            logger.info("✓ Funkcia get_best_contract_for_instrument existuje")
            
            # Skontroluj typ funkcie
            func = getattr(utils, 'get_best_contract_for_instrument')
            logger.info(f"✓ Typ funkcie: {type(func)}")
            
            # Skontroluj docstring
            if func.__doc__:
                logger.info(f"✓ Docstring: {func.__doc__[:100]}...")
            else:
                logger.warning("⚠ Funkcia nemá docstring")
                
        else:
            logger.error("✗ Funkcia get_best_contract_for_instrument NEEXISTUJE!")
            
            # Zobraz všetky dostupné funkcie
            logger.info("Dostupné funkcie v utils:")
            for attr in dir(utils):
                if callable(getattr(utils, attr)) and not attr.startswith('_'):
                    logger.info(f"  - {attr}")
        
        # Test reload modulu
        logger.info("\nTestujem reload modulu...")
        import importlib
        importlib.reload(utils)
        logger.info("✓ Modul utils reloadovaný")
        
        # Znovu skontroluj funkciu
        if hasattr(utils, 'get_best_contract_for_instrument'):
            logger.info("✓ Funkcia get_best_contract_for_instrument existuje po reload")
        else:
            logger.error("✗ Funkcia get_best_contract_for_instrument NEEXISTUJE ani po reload!")
            
    except Exception as e:
        logger.error(f"Chyba pri teste importu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
