#!/usr/bin/env python3
"""
Test skript pre overenie logovania
"""

import sys
import os

# Pridaj cestu k modulom
sys.path.append('/root/PY')

def test_logging_config():
    """Testuje konfiguráciu logovania"""
    
    print("=== TEST KONFIGURÁCIE LOGOVANIA ===")
    
    # Test 1: LOG_TO_FILE = False (default v config.py)
    print("\n1. Test s LOG_TO_FILE = False")
    try:
        import config
        import utils
        
        print(f"   LOG_TO_FILE = {getattr(config, 'LOG_TO_FILE', 'NEDEFINOVANÉ')}")
        
        # Inicializuj logovanie
        utils.setup_logging()
        
        # Test logovanie
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info("Test správa - táto by sa mala zobraziť len v konzole")
        logger.warning("Test warning - táto by sa mala zobraziť len v konzole")
        
        # Skontroluj či sa vytvoril camarilla.log
        if os.path.exists('camarilla.log'):
            print("   ❌ CHYBA: camarilla.log sa vytvoril aj keď LOG_TO_FILE = False")
        else:
            print("   ✅ OK: camarilla.log sa nevytvoril (LOG_TO_FILE = False)")
            
    except Exception as e:
        print(f"   ❌ CHYBA pri teste 1: {e}")
    
    # Test 2: Simulácia LOG_TO_FILE = True
    print("\n2. Test s LOG_TO_FILE = True (simulácia)")
    try:
        # Dočasne zmeň config
        original_value = getattr(config, 'LOG_TO_FILE', False)
        config.LOG_TO_FILE = True
        
        # Znovu inicializuj logovanie (musíme vyčistiť existujúce handlery)
        import importlib
        importlib.reload(utils)
        
        utils.setup_logging()
        
        logger = logging.getLogger("test_with_file")
        logger.info("Test správa - táto by sa mala zobraziť v konzole aj súbore")
        
        # Skontroluj či sa vytvoril camarilla.log
        if os.path.exists('camarilla.log'):
            print("   ✅ OK: camarilla.log sa vytvoril (LOG_TO_FILE = True)")
            
            # Skontroluj obsah
            with open('camarilla.log', 'r') as f:
                content = f.read()
                if "Test správa" in content:
                    print("   ✅ OK: Správa sa zapísala do súboru")
                else:
                    print("   ❌ CHYBA: Správa sa nezapísala do súboru")
        else:
            print("   ❌ CHYBA: camarilla.log sa nevytvoril aj keď LOG_TO_FILE = True")
        
        # Vráť pôvodnú hodnotu
        config.LOG_TO_FILE = original_value
        
    except Exception as e:
        print(f"   ❌ CHYBA pri teste 2: {e}")
    
    # Vyčisti test súbor
    if os.path.exists('camarilla.log'):
        os.remove('camarilla.log')
        print("\n🧹 Test súbor camarilla.log zmazaný")
    
    print("\n=== KONIEC TESTU LOGOVANIA ===")
    print("\nAko používať:")
    print("• LOG_TO_FILE = False → logovanie len do konzoly")
    print("• LOG_TO_FILE = True  → logovanie do konzoly + camarilla.log")

if __name__ == "__main__":
    test_logging_config()
