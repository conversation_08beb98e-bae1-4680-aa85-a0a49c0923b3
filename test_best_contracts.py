#!/usr/bin/env python3
"""
Test skript na výber najnovších najlikvidnejších kontraktov
Pre každý nástroj nájde najvhodnejší front month kontrakt
"""

import sys
import os
import logging
from datetime import datetime, timezone, timedelta
import pytz

# Pridaj cestu k modulom
sys.path.append('/root/PY')

from ib_insync import IB, Future
import config

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(name)s:%(lineno)d:%(funcName)s:%(message)s',
    handlers=[
        logging.FileHandler('test_best_contracts.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def get_contract_volume_and_oi(ib, contract):
    """Získa volume a open interest pre kontrakt"""
    try:
        # Skús získať market data
        ticker = ib.reqMktData(contract, '', False, False)
        ib.sleep(2)  # Počkaj na dáta

        volume = getattr(ticker, 'volume', 0) if hasattr(ticker, 'volume') else 0
        # Open interest nie je vždy dostupný cez ticker

        ib.cancelMktData(contract)
        return volume, 0  # Open interest zatiaľ nedostupný

    except Exception as e:
        logger.warning(f"Chyba pri získavaní volume pre {contract.localSymbol}: {e}")
        return 0, 0

def get_best_contract_for_symbol(ib, symbol, exchange):
    """Nájde najlepší kontrakt pre daný symbol"""
    logger.info(f"\n=== HĽADÁM NAJLEPŠÍ KONTRAKT PRE {symbol} ===")

    try:
        # Vytvor generický kontrakt
        generic_contract = Future(symbol=symbol, exchange=exchange, currency='USD')

        # Získaj všetky dostupné kontrakty
        all_contracts = ib.reqContractDetails(generic_contract)

        if not all_contracts:
            logger.error(f"Nenašiel som žiadne kontrakty pre {symbol}")
            return None

        logger.info(f"Našiel som {len(all_contracts)} kontraktov pre {symbol}")

        # Filtrovanie kontraktov
        now = datetime.now(timezone.utc)
        valid_contracts = []

        for contract_detail in all_contracts:
            contract = contract_detail.contract

            # Parsuj dátum expirácie
            try:
                expiry_str = contract.lastTradeDateOrContractMonth
                if len(expiry_str) == 8:  # YYYYMMDD
                    expiry_date = datetime.strptime(expiry_str, '%Y%m%d').replace(tzinfo=timezone.utc)
                elif len(expiry_str) == 6:  # YYYYMM
                    expiry_date = datetime.strptime(expiry_str + '01', '%Y%m%d').replace(tzinfo=timezone.utc)
                    # Pre YYYYMM, nastav na koniec mesiaca
                    next_month = expiry_date.replace(day=28) + timedelta(days=4)
                    expiry_date = next_month - timedelta(days=next_month.day)
                else:
                    logger.warning(f"Neznámy formát dátumu expirácie: {expiry_str}")
                    continue

                # Kontrola či kontrakt nie je expirovaný
                days_to_expiry = (expiry_date - now).days

                # Filtrovanie:
                # 1. Kontrakt nesmie byť expirovaný
                # 2. Kontrakt nesmie expírovať príliš skoro (min 7 dní)
                # 3. Kontrakt nesmie expírovať príliš ďaleko (max 6 mesiacov)
                if days_to_expiry < 7:
                    logger.debug(f"Preskakujem {contract.localSymbol} - expiruje príliš skoro ({days_to_expiry} dní)")
                    continue
                elif days_to_expiry > 180:
                    logger.debug(f"Preskakujem {contract.localSymbol} - expiruje príliš ďaleko ({days_to_expiry} dní)")
                    continue

                valid_contracts.append({
                    'contract': contract,
                    'expiry_date': expiry_date,
                    'days_to_expiry': days_to_expiry,
                    'local_symbol': contract.localSymbol
                })

                logger.info(f"  Kandidát: {contract.localSymbol} - expiruje za {days_to_expiry} dní ({expiry_date.strftime('%Y-%m-%d')})")

            except Exception as e:
                logger.warning(f"Chyba pri spracovaní kontraktu {contract.localSymbol}: {e}")
                continue

        if not valid_contracts:
            logger.error(f"Nenašiel som žiadne platné kontrakty pre {symbol}")
            return None

        # Zoraď kontrakty podľa dátumu expirácie (najnovší najskôr)
        valid_contracts.sort(key=lambda x: x['expiry_date'], reverse=True)

        logger.info(f"\nPlatné kontrakty pre {symbol} (zoradené podľa expirácie - najnovší najskôr):")
        for i, contract_info in enumerate(valid_contracts):
            logger.info(f"  {i+1}. {contract_info['local_symbol']} - {contract_info['days_to_expiry']} dní")

        # Vyber najlepší kontrakt
        # NOVÁ STRATÉGIA: Vždy vyber NAJNOVŠÍ dostupný kontrakt (najďalšia expirácia)
        # Toto zabezpečí najlikvidnejší kontrakt s najdlhším časom do expirácie

        best_contract = None

        # Stratégia: Vyber najnovší kontrakt (prvý v zoradenom zozname)
        if valid_contracts:
            best_contract = valid_contracts[0]  # Najnovší kontrakt
            logger.info(f"Vyberám NAJNOVŠÍ kontrakt: {best_contract['local_symbol']} s {best_contract['days_to_expiry']} dňami do expirácie")

        if best_contract:
            logger.info(f"\n>>> VYBRANÝ NAJLEPŠÍ KONTRAKT PRE {symbol}: {best_contract['local_symbol']} <<<")
            logger.info(f"    Expiruje za {best_contract['days_to_expiry']} dní ({best_contract['expiry_date'].strftime('%Y-%m-%d')})")

            # Skús získať volume info
            try:
                volume, oi = get_contract_volume_and_oi(ib, best_contract['contract'])
                if volume > 0:
                    logger.info(f"    Volume: {volume}")
            except Exception as e:
                logger.debug(f"Nepodarilo sa získať volume pre {best_contract['local_symbol']}: {e}")

            return best_contract['contract']
        else:
            logger.error(f"Nepodarilo sa vybrať najlepší kontrakt pre {symbol}")
            return None

    except Exception as e:
        logger.error(f"Chyba pri hľadaní najlepšieho kontraktu pre {symbol}: {e}")
        return None

def test_best_contracts():
    """Testuje výber najlepších kontraktov pre všetky nástroje"""

    # Pripoj sa k IB
    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=998)
        logger.info("Úspešne pripojený k IB")

        # Zoznam nástrojov z config
        instruments = []
        for inst in config.INSTRUMENTS_CONFIG:
            instruments.append({
                'symbol': inst['symbol'],
                'exchange': inst['exchange']
            })

        logger.info(f"\nTestujem {len(instruments)} nástrojov:")
        for inst in instruments:
            logger.info(f"  - {inst['symbol']} na {inst['exchange']}")

        # Pre každý nástroj nájdi najlepší kontrakt
        results = {}

        for inst in instruments:
            symbol = inst['symbol']
            exchange = inst['exchange']

            best_contract = get_best_contract_for_symbol(ib, symbol, exchange)

            if best_contract:
                results[symbol] = {
                    'contract': best_contract,
                    'local_symbol': best_contract.localSymbol,
                    'expiry': best_contract.lastTradeDateOrContractMonth,
                    'con_id': best_contract.conId
                }
            else:
                results[symbol] = None

        # Súhrn výsledkov
        logger.info("\n" + "="*60)
        logger.info("SÚHRN NAJLEPŠÍCH KONTRAKTOV")
        logger.info("="*60)

        for symbol, result in results.items():
            if result:
                logger.info(f"{symbol:>6}: {result['local_symbol']} (expiry: {result['expiry']}, conId: {result['con_id']})")
            else:
                logger.error(f"{symbol:>6}: NENÁJDENÝ!")

        # Test historických dát pre vybrané kontrakty
        logger.info("\n" + "="*60)
        logger.info("TEST HISTORICKÝCH DÁT PRE VYBRANÉ KONTRAKTY")
        logger.info("="*60)

        end_datetime = "20250529 02:00:00 UTC"

        for symbol, result in results.items():
            if result:
                logger.info(f"\n--- Test historických dát pre {symbol} ({result['local_symbol']}) ---")
                try:
                    bars = ib.reqHistoricalData(
                        result['contract'],
                        endDateTime=end_datetime,
                        durationStr='1 D',
                        barSizeSetting='1 hour',
                        whatToShow='ASK',
                        useRTH=False,
                        formatDate=1
                    )

                    logger.info(f"Získal som {len(bars)} sviečok:")
                    for bar in bars[-3:]:  # Zobraz posledné 3 sviečky
                        utc_time = bar.date.replace(tzinfo=timezone.utc)
                        ny_time = utc_time.astimezone(pytz.timezone('America/New_York'))

                        logger.info(f"  {utc_time.strftime('%Y-%m-%d %H:%M')} UTC | "
                                  f"{ny_time.strftime('%Y-%m-%d %H:%M')} NY | "
                                  f"O={bar.open:.2f} H={bar.high:.2f} L={bar.low:.2f} C={bar.close:.2f}")

                except Exception as e:
                    logger.error(f"Chyba pri získavaní historických dát pre {symbol}: {e}")

    except Exception as e:
        logger.error(f"Chyba pri pripojení k IB: {e}")
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == "__main__":
    logger.info("=== ŠTART TESTU NAJLEPŠÍCH KONTRAKTOV ===")
    test_best_contracts()
    logger.info("=== KONIEC TESTU NAJLEPŠÍCH KONTRAKTOV ===")
