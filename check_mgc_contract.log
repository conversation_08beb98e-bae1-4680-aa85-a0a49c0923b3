2025-05-29 11:17:14,755 INFO:__main__:184:<module>:=== ŠTART KONTROLY MGC KONTRAKTOV ===
2025-05-29 11:17:14,756 INFO:__main__:38:check_mgc_contracts:Pripájam sa k IB...
2025-05-29 11:17:14,756 INFO:ib_insync.client:204:connectAsync:Connecting to 127.0.0.1:4001 with clientId 999...
2025-05-29 11:17:14,757 INFO:ib_insync.client:212:connectAsync:Connected
2025-05-29 11:17:14,765 INFO:ib_insync.client:341:_onSocketHasData:Logged on to server version 176
2025-05-29 11:17:14,810 INFO:ib_insync.wrapper:1111:error:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
2025-05-29 11:17:14,811 INFO:ib_insync.wrapper:1111:error:Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.ushmds
2025-05-29 11:17:14,811 INFO:ib_insync.wrapper:1111:error:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
2025-05-29 11:17:14,812 INFO:ib_insync.client:218:connectAsync:API connection ready
2025-05-29 11:17:14,818 INFO:ib_insync.wrapper:301:position:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
2025-05-29 11:17:14,858 INFO:ib_insync.wrapper:301:position:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
2025-05-29 11:17:14,859 INFO:ib_insync.wrapper:301:position:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
2025-05-29 11:17:14,859 INFO:ib_insync.wrapper:301:position:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
2025-05-29 11:17:15,121 INFO:ib_insync.wrapper:288:updatePortfolio:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2111.5541992, marketValue=21115.54, averageCost=10456.12, unrealizedPNL=203.3, realizedPNL=0.0, account='DUK870453')
2025-05-29 11:17:15,122 INFO:ib_insync.wrapper:288:updatePortfolio:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6437.32, averageCost=6410.59, unrealizedPNL=-26.73, realizedPNL=0.0, account='DUK870453')
2025-05-29 11:17:15,122 INFO:ib_insync.wrapper:288:updatePortfolio:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5992.5, marketValue=59925.0, averageCost=29693.12, unrealizedPNL=538.76, realizedPNL=0.0, account='DUK870453')
2025-05-29 11:17:15,122 INFO:ib_insync.wrapper:288:updatePortfolio:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21798.3496094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 11:17:18,818 ERROR:ib_insync.ib:1779:connectAsync:completed orders request timed out
2025-05-29 11:17:18,833 INFO:ib_insync.wrapper:459:execDetails:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 11:17:18,833 INFO:ib_insync.wrapper:459:execDetails:execDetails Execution(execId='0000e1a7.********.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=**********, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 11:17:18,834 INFO:ib_insync.wrapper:504:commissionReport:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 11:17:18,834 INFO:ib_insync.wrapper:504:commissionReport:commissionReport: CommissionReport(execId='0000e1a7.********.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 11:17:18,834 INFO:ib_insync.ib:1789:connectAsync:Synchronization complete
2025-05-29 11:17:18,834 INFO:__main__:40:check_mgc_contracts:Úspešne pripojený k IB
2025-05-29 11:17:18,834 INFO:__main__:44:check_mgc_contracts:Aktuálny čas UTC: 2025-05-29 09:17:18.834766+00:00
2025-05-29 11:17:18,834 INFO:__main__:47:check_mgc_contracts:
=== KONTROLA get_front_contract_month() PRE MGC ===
2025-05-29 11:17:18,834 ERROR:__main__:98:check_mgc_contracts:Chyba pri get_front_contract_month('MGC'): get_front_contract_month() takes 0 positional arguments but 2 were given
2025-05-29 11:17:18,835 INFO:__main__:101:check_mgc_contracts:
=== KONTROLA DOSTUPNÝCH MGC KONTRAKTOV ===
2025-05-29 11:17:19,161 INFO:__main__:110:check_mgc_contracts:Našiel som 12 MGC kontraktov:
2025-05-29 11:17:19,162 INFO:__main__:114:check_mgc_contracts:  1. conId: 639786536
2025-05-29 11:17:19,163 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,163 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20250626
2025-05-29 11:17:19,163 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCM5
2025-05-29 11:17:19,163 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,163 INFO:__main__:122:check_mgc_contracts:     >>> TOTO JE JÚN 2025 KONTRAKT (M5) <<<
2025-05-29 11:17:19,163 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,163 INFO:__main__:114:check_mgc_contracts:  2. conId: 656780482
2025-05-29 11:17:19,163 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,163 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20250827
2025-05-29 11:17:19,164 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCQ5
2025-05-29 11:17:19,164 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,164 INFO:__main__:126:check_mgc_contracts:     >>> TOTO JE AUGUST 2025 KONTRAKT (Q5) <<<
2025-05-29 11:17:19,164 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,164 INFO:__main__:114:check_mgc_contracts:  3. conId: 668631622
2025-05-29 11:17:19,164 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,164 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20251029
2025-05-29 11:17:19,164 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCV5
2025-05-29 11:17:19,164 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,164 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,164 INFO:__main__:114:check_mgc_contracts:  4. conId: 674701641
2025-05-29 11:17:19,164 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,164 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20251229
2025-05-29 11:17:19,164 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCZ5
2025-05-29 11:17:19,165 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,165 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,165 INFO:__main__:114:check_mgc_contracts:  5. conId: 693609542
2025-05-29 11:17:19,165 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,165 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20260225
2025-05-29 11:17:19,165 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCG6
2025-05-29 11:17:19,165 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,166 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,166 INFO:__main__:114:check_mgc_contracts:  6. conId: 706903676
2025-05-29 11:17:19,166 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,166 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20260428
2025-05-29 11:17:19,166 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCJ6
2025-05-29 11:17:19,166 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,166 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,167 INFO:__main__:114:check_mgc_contracts:  7. conId: 712565978
2025-05-29 11:17:19,167 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,167 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20260626
2025-05-29 11:17:19,167 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCM6
2025-05-29 11:17:19,167 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,167 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,167 INFO:__main__:114:check_mgc_contracts:  8. conId: 732156883
2025-05-29 11:17:19,167 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,168 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20260827
2025-05-29 11:17:19,168 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCQ6
2025-05-29 11:17:19,168 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,168 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,168 INFO:__main__:114:check_mgc_contracts:  9. conId: 744880158
2025-05-29 11:17:19,168 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,168 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20261028
2025-05-29 11:17:19,168 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCV6
2025-05-29 11:17:19,169 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,169 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,169 INFO:__main__:114:check_mgc_contracts:  10. conId: 751494403
2025-05-29 11:17:19,169 INFO:__main__:115:check_mgc_contracts:     symbol: MGC
2025-05-29 11:17:19,169 INFO:__main__:116:check_mgc_contracts:     lastTradeDateOrContractMonth: 20261229
2025-05-29 11:17:19,169 INFO:__main__:117:check_mgc_contracts:     localSymbol: MGCZ6
2025-05-29 11:17:19,169 INFO:__main__:118:check_mgc_contracts:     multiplier: 10
2025-05-29 11:17:19,170 INFO:__main__:128:check_mgc_contracts:
2025-05-29 11:17:19,170 INFO:__main__:134:check_mgc_contracts:
=== POROVNANIE S INÝMI NÁSTROJMI ===
2025-05-29 11:17:19,170 ERROR:__main__:142:check_mgc_contracts:Chyba pri get_front_contract_month('M2K'): get_front_contract_month() takes 0 positional arguments but 2 were given
2025-05-29 11:17:19,170 ERROR:__main__:142:check_mgc_contracts:Chyba pri get_front_contract_month('MES'): get_front_contract_month() takes 0 positional arguments but 2 were given
2025-05-29 11:17:19,170 ERROR:__main__:142:check_mgc_contracts:Chyba pri get_front_contract_month('MNQ'): get_front_contract_month() takes 0 positional arguments but 2 were given
2025-05-29 11:17:19,170 INFO:__main__:145:check_mgc_contracts:
=== TEST HISTORICKÝCH DÁT PRE MGC ===
2025-05-29 11:17:19,171 INFO:ib_insync.ib:290:disconnect:Disconnecting from 127.0.0.1:4001, 164 B sent in 9 messages, 24.6 kB received in 328 messages, session time 4.41 s.
2025-05-29 11:17:19,171 INFO:ib_insync.client:230:disconnect:Disconnecting
2025-05-29 11:17:19,171 INFO:__main__:181:check_mgc_contracts:Odpojený od IB
2025-05-29 11:17:19,172 INFO:__main__:186:<module>:=== KONIEC KONTROLY MGC KONTRAKTOV ===
