#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
import pytz
from ib_insync import IB, Future, util
import pandas as pd

# Pridaj PY script adresár do cesty
sys.path.append(os.path.join(os.path.dirname(__file__), 'PY script'))

try:
    import config
    import utils
except ImportError as e:
    print(f"Chyba pri importovaní modulov: {e}")
    sys.exit(1)

# Nastavenie logovania do súboru
log_filename = 'debug_mgc_detailed_output.txt'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(filename)s:%(lineno)d:%(name)s:%(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(log_filename, mode='w', encoding='utf-8'),
        logging.StreamHandler()  # Aj do konzoly
    ]
)
logger = logging.getLogger(__name__)

def debug_mgc_detailed():
    """Detailná diagnostika MGC - porovnanie rôznych kontraktov a nastavení"""

    ib = IB()

    try:
        logger.info("=== DETAILNÁ DIAGNOSTIKA MGC ===")
        logger.info(f"Výstup sa zapisuje do súboru: {log_filename}")
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=996)
        logger.info("Úspešne pripojený k IB")

        # 1. Získaj všetky dostupné MGC kontrakty
        logger.info("\n--- 1. VŠETKY DOSTUPNÉ MGC KONTRAKTY ---")
        generic_mgc = Future(symbol='MGC', exchange='COMEX', currency='USD')
        all_mgc_contracts = ib.reqContractDetails(generic_mgc)

        logger.info(f"Našiel som {len(all_mgc_contracts)} MGC kontraktov:")

        valid_contracts = []
        now = datetime.now(timezone.utc)

        for contract_detail in all_mgc_contracts:
            contract = contract_detail.contract
            expiry_str = contract.lastTradeDateOrContractMonth

            try:
                if len(expiry_str) == 8:  # YYYYMMDD
                    expiry_date = datetime.strptime(expiry_str, '%Y%m%d').replace(tzinfo=timezone.utc)
                elif len(expiry_str) == 6:  # YYYYMM
                    expiry_date = datetime.strptime(expiry_str + '01', '%Y%m%d').replace(tzinfo=timezone.utc)
                    next_month = expiry_date.replace(day=28) + timedelta(days=4)
                    expiry_date = next_month - timedelta(days=next_month.day)
                else:
                    continue

                days_to_expiry = (expiry_date - now).days

                if days_to_expiry > -5 and days_to_expiry < 400:  # Zobraz aj nedávno expirované
                    valid_contracts.append({
                        'contract': contract,
                        'local_symbol': contract.localSymbol,
                        'expiry_date': expiry_date,
                        'days_to_expiry': days_to_expiry
                    })

                    status = "EXPIROVANÝ" if days_to_expiry < 0 else "AKTÍVNY"
                    logger.info(f"  {contract.localSymbol}: expiruje za {days_to_expiry} dní ({status})")

            except Exception as e:
                logger.warning(f"Chyba pri spracovaní {contract.localSymbol}: {e}")

        # Zoraď podľa expirácie
        valid_contracts.sort(key=lambda x: x['expiry_date'])

        # 2. Test rôznych kontraktov
        logger.info("\n--- 2. TEST RÔZNYCH KONTRAKTOV ---")

        # Testuj prvé 3 aktívne kontrakty
        active_contracts = [c for c in valid_contracts if c['days_to_expiry'] > 0][:3]

        for contract_info in active_contracts:
            contract = contract_info['contract']
            local_symbol = contract_info['local_symbol']
            days = contract_info['days_to_expiry']

            logger.info(f"\n=== TESTOVANIE {local_symbol} (expiruje za {days} dní) ===")

            # Test market data
            try:
                ticker = ib.reqMktData(contract, '', False, False)
                ib.sleep(2)

                logger.info(f"[{local_symbol}] Market data:")
                logger.info(f"  Last: {ticker.last}")
                logger.info(f"  Bid: {ticker.bid}")
                logger.info(f"  Ask: {ticker.ask}")
                logger.info(f"  Close: {ticker.close}")

                ib.cancelMktData(contract)

            except Exception as e:
                logger.error(f"[{local_symbol}] Chyba pri market data: {e}")

            # Test historických dát s rôznymi nastaveniami
            test_settings = [
                {'whatToShow': 'TRADES', 'useRTH': True, 'desc': 'TRADES + RTH'},
                {'whatToShow': 'TRADES', 'useRTH': False, 'desc': 'TRADES + non-RTH'},
                {'whatToShow': 'ASK', 'useRTH': True, 'desc': 'ASK + RTH'},
                {'whatToShow': 'ASK', 'useRTH': False, 'desc': 'ASK + non-RTH'},
                {'whatToShow': 'BID', 'useRTH': False, 'desc': 'BID + non-RTH'},
            ]

            for setting in test_settings:
                try:
                    logger.info(f"\n[{local_symbol}] Test: {setting['desc']}")

                    bars = ib.reqHistoricalData(
                        contract,
                        '',
                        '3 D',
                        '1 day',
                        setting['whatToShow'],
                        setting['useRTH'],
                        formatDate=1
                    )

                    if bars and len(bars) >= 2:
                        # Zobraz posledné 2 denné sviečky
                        for i, bar in enumerate(bars[-2:]):
                            bar_date = bar.date
                            if hasattr(bar_date, 'strftime'):
                                date_str = bar_date.strftime('%Y-%m-%d')
                            else:
                                date_str = str(bar_date)

                            logger.info(f"    [{i}] {date_str}: "
                                      f"O={bar.open:.1f} H={bar.high:.1f} L={bar.low:.1f} C={bar.close:.1f}")

                        # Porovnaj s očakávanými hodnotami
                        latest_bar = bars[-1]
                        expected_high = 3351.9
                        expected_low = 3301.2
                        expected_close = 3312.0

                        high_diff = abs(latest_bar.high - expected_high)
                        low_diff = abs(latest_bar.low - expected_low)
                        close_diff = abs(latest_bar.close - expected_close)

                        logger.info(f"    Porovnanie s očakávanými hodnotami:")
                        logger.info(f"      High: {latest_bar.high:.1f} vs {expected_high} (diff: {high_diff:.1f})")
                        logger.info(f"      Low:  {latest_bar.low:.1f} vs {expected_low} (diff: {low_diff:.1f})")
                        logger.info(f"      Close: {latest_bar.close:.1f} vs {expected_close} (diff: {close_diff:.1f})")

                        if high_diff < 5 and low_diff < 5 and close_diff < 5:
                            logger.info(f"    ✅ ZHODA! Toto nastavenie dáva správne ceny")
                        else:
                            logger.warning(f"    ❌ NEZHODA - rozdiely sú príliš veľké")
                    else:
                        logger.warning(f"    Nedostatok dát: {len(bars) if bars else 0} sviečok")

                except Exception as e:
                    logger.error(f"    Chyba: {e}")

                ib.sleep(1)  # Pauza medzi požiadavkami

        # 3. Test iných symbolov pre zlato
        logger.info("\n--- 3. TEST INÝCH ZLATÝCH KONTRAKTOV ---")

        other_gold_symbols = ['GC', 'QO']  # GC = full size gold, QO = E-mini gold

        for symbol in other_gold_symbols:
            try:
                logger.info(f"\n=== TESTOVANIE {symbol} ===")

                if symbol == 'GC':
                    exchange = 'COMEX'
                elif symbol == 'QO':
                    exchange = 'COMEX'
                else:
                    continue

                generic_contract = Future(symbol=symbol, exchange=exchange, currency='USD')
                contract_details = ib.reqContractDetails(generic_contract)

                if contract_details:
                    # Vyber prvý dostupný kontrakt
                    contract = contract_details[0].contract
                    logger.info(f"[{symbol}] Našiel som kontrakt: {contract.localSymbol}")

                    # Test market data
                    ticker = ib.reqMktData(contract, '', False, False)
                    ib.sleep(2)

                    logger.info(f"[{symbol}] Market data:")
                    logger.info(f"  Last: {ticker.last}")
                    logger.info(f"  Close: {ticker.close}")

                    ib.cancelMktData(contract)

                else:
                    logger.info(f"[{symbol}] Žiadne kontrakty nenájdené")

            except Exception as e:
                logger.error(f"[{symbol}] Chyba: {e}")

        logger.info("\n--- ZÁVER ---")
        logger.info("Hľadajte nastavenie s ✅ ZHODA! - to je správna kombinácia")
        logger.info("kontraktu a parametrov pre získanie správnych cien.")
        logger.info(f"\nKompletný výstup bol uložený do súboru: {log_filename}")

    except Exception as e:
        logger.error(f"Kritická chyba: {e}", exc_info=True)

    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")
        logger.info(f"\n=== KONIEC - Výsledky v súbore {log_filename} ===")

if __name__ == '__main__':
    debug_mgc_detailed()
