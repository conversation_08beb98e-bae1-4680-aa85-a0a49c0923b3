#!/usr/bin/env python3
"""
Test skript pre novú logiku výberu kontraktov
Testuje či hlavný skript správne používa najnovšie kontrakty
"""

import sys
import os
import logging
from datetime import datetime, timezone
import pytz

# Pridaj cestu k modulom
sys.path.append('/root/PY')

from ib_insync import IB
import config
import utils

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(name)s:%(lineno)d:%(funcName)s:%(message)s',
    handlers=[
        logging.FileHandler('test_new_contract_logic.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_new_contract_logic():
    """Testuje novú logiku výberu kontraktov"""
    
    # Pripoj sa k IB
    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=997)
        logger.info("Úspešne pripojený k IB")
        
        logger.info("\n=== TEST NOVEJ LOGIKY VÝBERU KONTRAKTOV ===")
        
        # Test novej funkcie pre každý nástroj
        instruments = config.INSTRUMENTS_CONFIG
        
        logger.info(f"Testujem {len(instruments)} nástrojov:")
        for inst in instruments:
            logger.info(f"  - {inst['symbol']} na {inst['exchange']}")
        
        results = {}
        
        for inst in instruments:
            symbol = inst['symbol']
            exchange = inst['exchange']
            currency = inst.get('currency', 'USD')
            
            logger.info(f"\n--- Test pre {symbol} ---")
            
            # Test novej funkcie
            contract = utils.get_best_contract_for_instrument(ib, symbol, exchange, currency)
            
            if contract:
                results[symbol] = {
                    'success': True,
                    'local_symbol': contract.localSymbol,
                    'expiry': contract.lastTradeDateOrContractMonth,
                    'con_id': contract.conId
                }
                logger.info(f"✓ {symbol}: Úspešne načítaný {contract.localSymbol}")
            else:
                results[symbol] = {
                    'success': False,
                    'error': 'Nepodarilo sa načítať kontrakt'
                }
                logger.error(f"✗ {symbol}: Nepodarilo sa načítať kontrakt")
        
        # Súhrn výsledkov
        logger.info("\n" + "="*60)
        logger.info("SÚHRN TESTOV NOVEJ LOGIKY")
        logger.info("="*60)
        
        success_count = 0
        for symbol, result in results.items():
            if result['success']:
                logger.info(f"✓ {symbol:>6}: {result['local_symbol']} (expiry: {result['expiry']}, conId: {result['con_id']})")
                success_count += 1
            else:
                logger.error(f"✗ {symbol:>6}: {result['error']}")
        
        logger.info(f"\nÚspešnosť: {success_count}/{len(instruments)} ({success_count/len(instruments)*100:.1f}%)")
        
        # Porovnanie so starými kontraktmi
        logger.info("\n" + "="*60)
        logger.info("POROVNANIE SO STARÝMI KONTRAKTMI")
        logger.info("="*60)
        
        # Stará logika
        try:
            old_expiry = utils.get_front_contract_month()
            logger.info(f"Stará logika by použila expiráciu: {old_expiry}")
            
            # Pre každý nástroj ukáž rozdiel
            for symbol, result in results.items():
                if result['success']:
                    new_expiry = result['expiry']
                    logger.info(f"{symbol}: Stará={old_expiry} vs Nová={new_expiry}")
                    
                    if new_expiry != old_expiry:
                        logger.info(f"  → {symbol} používa NOVŠÍ kontrakt! ✓")
                    else:
                        logger.warning(f"  → {symbol} používa rovnaký kontrakt")
                        
        except Exception as e:
            logger.error(f"Chyba pri testovaní starej logiky: {e}")
        
        # Test historických dát pre nové kontrakty
        logger.info("\n" + "="*60)
        logger.info("TEST HISTORICKÝCH DÁT PRE NOVÉ KONTRAKTY")
        logger.info("="*60)
        
        end_datetime = "20250529 02:00:00 UTC"
        
        for symbol, result in results.items():
            if result['success']:
                logger.info(f"\n--- Test historických dát pre {symbol} ({result['local_symbol']}) ---")
                try:
                    # Znovu načítaj kontrakt pre historické dáta
                    inst_config = next(inst for inst in instruments if inst['symbol'] == symbol)
                    contract = utils.get_best_contract_for_instrument(
                        ib, symbol, inst_config['exchange'], inst_config.get('currency', 'USD')
                    )
                    
                    if contract:
                        bars = ib.reqHistoricalData(
                            contract,
                            endDateTime=end_datetime,
                            durationStr='1 D',
                            barSizeSetting='1 hour',
                            whatToShow='ASK',
                            useRTH=False,
                            formatDate=1
                        )
                        
                        logger.info(f"Získal som {len(bars)} sviečok:")
                        for bar in bars[-3:]:  # Zobraz posledné 3 sviečky
                            utc_time = bar.date.replace(tzinfo=timezone.utc)
                            ny_time = utc_time.astimezone(pytz.timezone('America/New_York'))
                            
                            logger.info(f"  {utc_time.strftime('%Y-%m-%d %H:%M')} UTC | "
                                      f"{ny_time.strftime('%Y-%m-%d %H:%M')} NY | "
                                      f"O={bar.open:.2f} H={bar.high:.2f} L={bar.low:.2f} C={bar.close:.2f}")
                        
                        # Špeciálna kontrola pre MGC
                        if symbol == 'MGC':
                            logger.info(f"\n🔍 ŠPECIÁLNA KONTROLA PRE MGC:")
                            logger.info(f"   Kontrakt: {contract.localSymbol}")
                            logger.info(f"   Expirácia: {contract.lastTradeDateOrContractMonth}")
                            
                            # Kontrola či sú dáta rozumné (nie tie nezmyselné z pôvodného problému)
                            if bars:
                                last_bar = bars[-1]
                                if last_bar.close > 3000 and last_bar.close < 4000:
                                    logger.info(f"   ✓ Dáta vyzerajú rozumne (close={last_bar.close:.2f})")
                                else:
                                    logger.warning(f"   ⚠ Dáta môžu byť nesprávne (close={last_bar.close:.2f})")
                            
                except Exception as e:
                    logger.error(f"Chyba pri získavaní historických dát pre {symbol}: {e}")
        
        # Finálne zhodnotenie
        logger.info("\n" + "="*60)
        logger.info("FINÁLNE ZHODNOTENIE")
        logger.info("="*60)
        
        if success_count == len(instruments):
            logger.info("✅ VŠETKY TESTY ÚSPEŠNÉ!")
            logger.info("   Nová logika výberu kontraktov funguje správne")
            logger.info("   Všetky nástroje používajú najnovšie dostupné kontrakty")
        else:
            logger.warning(f"⚠️  ČIASTOČNÝ ÚSPECH: {success_count}/{len(instruments)} nástrojov")
            logger.warning("   Niektoré nástroje majú problémy s načítaním kontraktov")
        
    except Exception as e:
        logger.error(f"Chyba pri pripojení k IB: {e}")
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == "__main__":
    logger.info("=== ŠTART TESTU NOVEJ LOGIKY VÝBERU KONTRAKTOV ===")
    test_new_contract_logic()
    logger.info("=== KONIEC TESTU NOVEJ LOGIKY VÝBERU KONTRAKTOV ===")
