(venv) trader@ubuntu-4gb-fsn1-2:~/mes_bot$ python debug_mgc_data.py
2025-05-29 18:13:42 INFO:debug_mgc_data.py:36:__main__:=== DIAGNOSTIKA MGC DÁT ===
2025-05-29 18:13:42 INFO:debug_mgc_data.py:37:__main__:Pripájam sa k IB...
2025-05-29 18:13:42 INFO:client.py:204:ib_insync.client:Connecting to 127.0.0.1:4001 with clientId 998...
2025-05-29 18:13:42 INFO:client.py:212:ib_insync.client:Connected
2025-05-29 18:13:42 INFO:client.py:341:ib_insync.client:Logged on to server version 176
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.ushmds
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
2025-05-29 18:13:42 INFO:client.py:218:ib_insync.client:API connection ready
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2069.5, marketValue=20695.0, averageCost=10456.12, unrealizedPNL=-217.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6449738, marketValue=-6449.74, averageCost=6410.59, unrealizedPNL=-39.15, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5906.5, marketValue=59065.0, averageCost=29693.12, unrealizedPNL=-321.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21393.3496094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 18:13:46 ERROR:ib.py:1779:ib_insync.ib:completed orders request timed out
2025-05-29 18:13:46 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:13:46 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.********.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=**********, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:13:46 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:13:46 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.********.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:13:46 INFO:ib.py:1789:ib_insync.ib:Synchronization complete
2025-05-29 18:13:46 INFO:debug_mgc_data.py:39:__main__:Úspešne pripojený k IB
2025-05-29 18:13:46 INFO:debug_mgc_data.py:42:__main__:
--- 1. ZÍSKANIE NAJLEPŠIEHO MGC KONTRAKTU ---
2025-05-29 18:13:46 INFO:utils.py:135:utils:[MGC] Hľadám najlepší kontrakt na COMEX...
2025-05-29 18:13:47 INFO:utils.py:207:utils:[MGC] Vybraný najlepší kontrakt: MGCJ6 (expiruje za 333 dní - 2026-04-28)
2025-05-29 18:13:47 INFO:debug_mgc_data.py:49:__main__:Vybraný MGC kontrakt: MGCJ6
2025-05-29 18:13:47 INFO:debug_mgc_data.py:50:__main__:ConId: 706903676
2025-05-29 18:13:47 INFO:debug_mgc_data.py:51:__main__:LastTradeDateOrContractMonth: *************-05-29 18:13:47 INFO:debug_mgc_data.py:54:__main__:
--- 2. DETAILY KONTRAKTU ---
2025-05-29 18:13:47 INFO:debug_mgc_data.py:59:__main__:TimeZoneId: US/Eastern
2025-05-29 18:13:47 INFO:debug_mgc_data.py:60:__main__:TradingHours: 20250527:1800-20250528:1700;20250528:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700
2025-05-29 18:13:47 INFO:debug_mgc_data.py:61:__main__:LiquidHours: 20250528:0930-20250528:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700
2025-05-29 18:13:47 INFO:debug_mgc_data.py:66:__main__:
--- 3. AKTUÁLNE MARKET DATA ---
2025-05-29 18:13:47 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2119, reqId -1: Market data farm is connecting:usfuture
2025-05-29 18:13:48 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfuture
2025-05-29 18:13:49 INFO:debug_mgc_data.py:71:__main__:Last: 3452.5
2025-05-29 18:13:49 INFO:debug_mgc_data.py:72:__main__:Bid: 3456.9
2025-05-29 18:13:49 INFO:debug_mgc_data.py:73:__main__:Ask: 3458.5
2025-05-29 18:13:49 INFO:debug_mgc_data.py:74:__main__:Close: 3429.4
2025-05-29 18:13:49 INFO:debug_mgc_data.py:81:__main__:
--- 4. DENNÉ HISTORICKÉ DÁTA ---
2025-05-29 18:13:49 INFO:debug_mgc_data.py:83:__main__:Požadujem denné dáta s parametrami z hlavného skriptu:
2025-05-29 18:13:49 INFO:debug_mgc_data.py:84:__main__:  durationStr='5 D', barSizeSetting='1 day', whatToShow='ASK', useRTH=False
2025-05-29 18:13:50 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
2025-05-29 18:13:50 INFO:debug_mgc_data.py:96:__main__:Získal som 5 denných sviečok:
2025-05-29 18:13:50 ERROR:debug_mgc_data.py:147:__main__:Chyba pri získavaní denných dát: 'tzinfo' is an invalid keyword argument for replace()
2025-05-29 18:13:50 INFO:debug_mgc_data.py:150:__main__:
--- 6. HODINOVÉ DÁTA PRE POROVNANIE ---
2025-05-29 18:13:50 INFO:debug_mgc_data.py:162:__main__:Získal som 42 hodinových sviečok
2025-05-29 18:13:50 INFO:debug_mgc_data.py:163:__main__:Posledných 5 hodinových sviečok:
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 08:00 UTC | 2025-05-29 04:00 NY | O=3430.80 H=3448.10 L=3429.70 C=3447.20
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 09:00 UTC | 2025-05-29 05:00 NY | O=3447.20 H=3452.80 L=3441.20 C=3443.60
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 10:00 UTC | 2025-05-29 06:00 NY | O=3443.60 H=3450.60 L=3434.50 C=3440.20
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 11:00 UTC | 2025-05-29 07:00 NY | O=3440.20 H=3452.00 L=3439.70 C=3451.70
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 12:00 UTC | 2025-05-29 08:00 NY | O=3451.70 H=3459.40 L=3449.80 C=3458.30
2025-05-29 18:13:50 INFO:debug_mgc_data.py:177:__main__:
--- 7. KONTROLA REÁLNYCH TRŽNÝCH CIEN ---
2025-05-29 18:13:50 INFO:debug_mgc_data.py:178:__main__:Porovnajte získané ceny s:
2025-05-29 18:13:50 INFO:debug_mgc_data.py:179:__main__:  - CME Group: https://www.cmegroup.com/markets/metals/precious/gold.html
2025-05-29 18:13:50 INFO:debug_mgc_data.py:180:__main__:  - TradingView: https://www.tradingview.com/symbols/COMEX-GC1!/
2025-05-29 18:13:50 INFO:debug_mgc_data.py:181:__main__:  - Yahoo Finance: https://finance.yahoo.com/quote/GC%3DF/
2025-05-29 18:13:50 INFO:debug_mgc_data.py:185:__main__:
Aktuálny close z IB: 3458.30
2025-05-29 18:13:50 INFO:debug_mgc_data.py:186:__main__:Očakávaný rozsah pre zlato: 2300-2800 USD/oz
2025-05-29 18:13:50 WARNING:debug_mgc_data.py:189:__main__:⚠️  POZOR: Cena je mimo očakávaného rozsahu pre zlato!
2025-05-29 18:13:50 INFO:ib.py:290:ib_insync.ib:Disconnecting from 127.0.0.1:4001, 499 B sent in 14 messages, 29.4 kB received in 358 messages, session time 7.82 s.
2025-05-29 18:13:50 INFO:client.py:230:ib_insync.client:Disconnecting
2025-05-29 18:13:50 INFO:debug_mgc_data.py:199:__main__:Odpojený od IB
(venv) trader@ubuntu-4gb-fsn1-2:~/mes_bot$ ^C
(venv) trader@ubuntu-4gb-fsn1-2:~/mes_bot$ python test_all_instruments_contracts.py
2025-05-29 18:32:35 INFO:test_all_instruments_contracts.py:36:__main__:=== TEST VŠETKÝCH INŠTRUMENTOV ===
2025-05-29 18:32:35 INFO:test_all_instruments_contracts.py:37:__main__:Pripájam sa k IB...
2025-05-29 18:32:35 INFO:client.py:204:ib_insync.client:Connecting to 127.0.0.1:4001 with clientId 997...
2025-05-29 18:32:35 INFO:client.py:212:ib_insync.client:Connected
2025-05-29 18:32:35 INFO:client.py:341:ib_insync.client:Logged on to server version 176
2025-05-29 18:32:35 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
2025-05-29 18:32:35 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.ushmds
2025-05-29 18:32:35 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
2025-05-29 18:32:35 INFO:client.py:218:ib_insync.client:API connection ready
2025-05-29 18:32:35 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
2025-05-29 18:32:35 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
2025-05-29 18:32:35 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
2025-05-29 18:32:35 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
2025-05-29 18:32:35 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2072.********, marketValue=20727.0, averageCost=10456.12, unrealizedPNL=-185.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:32:35 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.********, marketValue=-6449.28, averageCost=6410.59, unrealizedPNL=-38.69, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:32:35 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5916.5, marketValue=59165.0, averageCost=29693.12, unrealizedPNL=-221.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:32:35 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21439.5, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 18:32:39 ERROR:ib.py:1779:ib_insync.ib:completed orders request timed out
2025-05-29 18:32:39 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:32:39 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.********.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=**********, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:32:39 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:32:39 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.********.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:32:39 INFO:ib.py:1789:ib_insync.ib:Synchronization complete
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:39:__main__:Úspešne pripojený k IB
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:51:__main__:
============================================================
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:52:__main__:TESTOVANIE: M2K na CME
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:53:__main__:============================================================
2025-05-29 18:32:39 INFO:utils.py:139:utils:[M2K] Hľadám najlepší kontrakt na CME...
2025-05-29 18:32:39 INFO:utils.py:218:utils:[M2K] Vybraný aktívny kontrakt: M2KM5 (expiruje za 21 dní)
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:64:__main__:[M2K] Vybraný kontrakt: M2KM5
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:65:__main__:[M2K] ConId: *********
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:66:__main__:[M2K] Expirácia: ********
2025-05-29 18:32:39 INFO:test_all_instruments_contracts.py:69:__main__:
--- AKTUÁLNE CENY PRE M2K ---
2025-05-29 18:32:39 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2119, reqId -1: Market data farm is connecting:usfuture
2025-05-29 18:32:40 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfuture
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:79:__main__:[M2K] Last: 2072.0
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:80:__main__:[M2K] Bid: 2072.0
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:81:__main__:[M2K] Ask: 2072.2
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:82:__main__:[M2K] Close: 2072.3
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:98:__main__:[M2K] Kontrola cien: OK
2025-05-29 18:32:41 INFO:test_all_instruments_contracts.py:106:__main__:
--- HISTORICKÉ DÁTA PRE M2K ---
2025-05-29 18:32:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:119:__main__:[M2K] Získal som 5 denných sviečok
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:123:__main__:[M2K] Posledná denná sviečka: O=2074.00 H=2130.60 L=2061.20 C=2072.20
2025-05-29 18:32:42 INFO:utils.py:273:utils:[M2K] Camarilla pivoty: Predch.deň H=2097.60, L=2069.00, C=2070.20, Rng=28.60 => H4=2085.93, L4=2054.47
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:132:__main__:[M2K] Camarilla pivoty: H4=2085.93, L4=2054.47
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:147:__main__:[M2K] Získal som 19 hodinových sviečok
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:151:__main__:[M2K] Posledná hodinová sviečka: O=2065.10 H=2073.20 L=2061.20 C=2072.20
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:51:__main__:
============================================================
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:52:__main__:TESTOVANIE: MES na CME
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:53:__main__:============================================================
2025-05-29 18:32:42 INFO:utils.py:139:utils:[MES] Hľadám najlepší kontrakt na CME...
2025-05-29 18:32:42 INFO:utils.py:218:utils:[MES] Vybraný aktívny kontrakt: MESM5 (expiruje za 21 dní)
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:64:__main__:[MES] Vybraný kontrakt: MESM5
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:65:__main__:[MES] ConId: *********
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:66:__main__:[MES] Expirácia: ********
2025-05-29 18:32:42 INFO:test_all_instruments_contracts.py:69:__main__:
--- AKTUÁLNE CENY PRE MES ---
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:79:__main__:[MES] Last: 5914.75
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:80:__main__:[MES] Bid: 5914.5
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:81:__main__:[MES] Ask: 5914.75
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:82:__main__:[MES] Close: 5902.75
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:98:__main__:[MES] Kontrola cien: OK
2025-05-29 18:32:44 INFO:test_all_instruments_contracts.py:106:__main__:
--- HISTORICKÉ DÁTA PRE MES ---
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:119:__main__:[MES] Získal som 5 denných sviečok
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:123:__main__:[MES] Posledná denná sviečka: O=5924.25 H=6008.00 L=5884.00 C=5914.75
2025-05-29 18:32:45 INFO:utils.py:273:utils:[MES] Camarilla pivoty: Predch.deň H=5952.50, L=5891.50, C=5926.00, Rng=61.00 => H4=5959.55, L4=5892.45
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:132:__main__:[MES] Camarilla pivoty: H4=5959.55, L4=5892.45
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:147:__main__:[MES] Získal som 19 hodinových sviečok
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:151:__main__:[MES] Posledná hodinová sviečka: O=5895.25 H=5918.00 L=5884.00 C=5914.75
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:51:__main__:
============================================================
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:52:__main__:TESTOVANIE: MNQ na CME
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:53:__main__:============================================================
2025-05-29 18:32:45 INFO:utils.py:139:utils:[MNQ] Hľadám najlepší kontrakt na CME...
2025-05-29 18:32:45 INFO:utils.py:218:utils:[MNQ] Vybraný aktívny kontrakt: MNQM5 (expiruje za 21 dní)
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:64:__main__:[MNQ] Vybraný kontrakt: MNQM5
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:65:__main__:[MNQ] ConId: *********
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:66:__main__:[MNQ] Expirácia: ********
2025-05-29 18:32:45 INFO:test_all_instruments_contracts.py:69:__main__:
--- AKTUÁLNE CENY PRE MNQ ---
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:79:__main__:[MNQ] Last: 21432.0
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:80:__main__:[MNQ] Bid: 21432.0
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:81:__main__:[MNQ] Ask: 21432.5
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:82:__main__:[MNQ] Close: 21379.75
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:98:__main__:[MNQ] Kontrola cien: OK
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:106:__main__:
--- HISTORICKÉ DÁTA PRE MNQ ---
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:119:__main__:[MNQ] Získal som 5 denných sviečok
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:123:__main__:[MNQ] Posledná denná sviečka: O=21518.00 H=21859.00 L=21301.75 C=21431.00
2025-05-29 18:32:47 INFO:utils.py:273:utils:[MNQ] Camarilla pivoty: Predch.deň H=21564.75, L=21322.50, C=21525.25, Rng=242.25 => H4=21658.49, L4=21392.01
2025-05-29 18:32:47 INFO:test_all_instruments_contracts.py:132:__main__:[MNQ] Camarilla pivoty: H4=21658.49, L4=21392.01
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:147:__main__:[MNQ] Získal som 19 hodinových sviečok
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:151:__main__:[MNQ] Posledná hodinová sviečka: O=21348.75 H=21445.50 L=21301.75 C=21431.00
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:51:__main__:
============================================================
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:52:__main__:TESTOVANIE: MGC na COMEX
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:53:__main__:============================================================
2025-05-29 18:32:48 INFO:utils.py:139:utils:[MGC] Hľadám najlepší kontrakt na COMEX...
2025-05-29 18:32:48 INFO:utils.py:218:utils:[MGC] Vybraný aktívny kontrakt: MGCM5 (expiruje za 27 dní)
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:64:__main__:[MGC] Vybraný kontrakt: MGCM5
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:65:__main__:[MGC] ConId: 639786536
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:66:__main__:[MGC] Expirácia: 20250626
2025-05-29 18:32:48 INFO:test_all_instruments_contracts.py:69:__main__:
--- AKTUÁLNE CENY PRE MGC ---
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:79:__main__:[MGC] Last: 3324.1
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:80:__main__:[MGC] Bid: 3324.0
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:81:__main__:[MGC] Ask: 3324.2
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:82:__main__:[MGC] Close: 3294.9
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:98:__main__:[MGC] Kontrola cien: PODOZRIVÉ - mimo rozsahu 2000-3000
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:106:__main__:
--- HISTORICKÉ DÁTA PRE MGC ---
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:119:__main__:[MGC] Získal som 5 denných sviečok
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:123:__main__:[MGC] Posledná denná sviečka: O=3282.20 H=3329.50 L=3242.30 C=3324.20
2025-05-29 18:32:50 INFO:utils.py:273:utils:[MGC] Camarilla pivoty: Predch.deň H=3324.60, L=3274.50, C=3285.10, Rng=50.10 => H4=3312.65, L4=3257.55
2025-05-29 18:32:50 INFO:test_all_instruments_contracts.py:132:__main__:[MGC] Camarilla pivoty: H4=3312.65, L4=3257.55
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:147:__main__:[MGC] Získal som 19 hodinových sviečok
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:151:__main__:[MGC] Posledná hodinová sviečka: O=3319.80 H=3329.50 L=3317.90 C=3324.20
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:177:__main__:
================================================================================
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:178:__main__:SÚHRN VÝSLEDKOV
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:179:__main__:================================================================================
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:183:__main__:   M2K:      M2KM5 | Cena:   2072.0 | Kontrola:                   OK | Dáta: OK
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:183:__main__:   MES:      MESM5 | Cena:  5914.75 | Kontrola:                   OK | Dáta: OK
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:183:__main__:   MNQ:      MNQM5 | Cena:  21432.0 | Kontrola:                   OK | Dáta: OK
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:183:__main__:   MGC:      MGCM5 | Cena:   3324.1 | Kontrola: PODOZRIVÉ - mimo rozsahu 2000-3000 | Dáta: OK
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:190:__main__:
================================================================================
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:191:__main__:ODPORÚČANIA:
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:192:__main__:================================================================================
2025-05-29 18:32:51 WARNING:test_all_instruments_contracts.py:204:__main__:Inštrumenty s problémami:
2025-05-29 18:32:51 WARNING:test_all_instruments_contracts.py:206:__main__:  - MGC (podozrivé ceny)
2025-05-29 18:32:51 INFO:ib.py:290:ib_insync.ib:Disconnecting from 127.0.0.1:4001, 1.33 kB sent in 28 messages, 47.2 kB received in 527 messages, session time 16.1 s.
2025-05-29 18:32:51 INFO:client.py:230:ib_insync.client:Disconnecting
2025-05-29 18:32:51 INFO:test_all_instruments_contracts.py:216:__main__:Odpojený od IB
