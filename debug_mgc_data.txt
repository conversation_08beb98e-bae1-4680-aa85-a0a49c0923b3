(venv) trader@ubuntu-4gb-fsn1-2:~/mes_bot$ python debug_mgc_data.py
2025-05-29 18:13:42 INFO:debug_mgc_data.py:36:__main__:=== DIAGNOSTIKA MGC DÁT ===
2025-05-29 18:13:42 INFO:debug_mgc_data.py:37:__main__:Pripájam sa k IB...
2025-05-29 18:13:42 INFO:client.py:204:ib_insync.client:Connecting to 127.0.0.1:4001 with clientId 998...
2025-05-29 18:13:42 INFO:client.py:212:ib_insync.client:Connected
2025-05-29 18:13:42 INFO:client.py:341:ib_insync.client:Logged on to server version 176
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.ushmds
2025-05-29 18:13:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
2025-05-29 18:13:42 INFO:client.py:218:ib_insync.client:API connection ready
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
2025-05-29 18:13:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2069.5, marketValue=20695.0, averageCost=10456.12, unrealizedPNL=-217.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6449738, marketValue=-6449.74, averageCost=6410.59, unrealizedPNL=-39.15, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5906.5, marketValue=59065.0, averageCost=29693.12, unrealizedPNL=-321.24, realizedPNL=0.0, account='DUK870453')
2025-05-29 18:13:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21393.3496094, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 18:13:46 ERROR:ib.py:1779:ib_insync.ib:completed orders request timed out
2025-05-29 18:13:46 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:13:46 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.********.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=**********, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 18:13:46 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:13:46 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.********.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 18:13:46 INFO:ib.py:1789:ib_insync.ib:Synchronization complete
2025-05-29 18:13:46 INFO:debug_mgc_data.py:39:__main__:Úspešne pripojený k IB
2025-05-29 18:13:46 INFO:debug_mgc_data.py:42:__main__:
--- 1. ZÍSKANIE NAJLEPŠIEHO MGC KONTRAKTU ---
2025-05-29 18:13:46 INFO:utils.py:135:utils:[MGC] Hľadám najlepší kontrakt na COMEX...
2025-05-29 18:13:47 INFO:utils.py:207:utils:[MGC] Vybraný najlepší kontrakt: MGCJ6 (expiruje za 333 dní - 2026-04-28)
2025-05-29 18:13:47 INFO:debug_mgc_data.py:49:__main__:Vybraný MGC kontrakt: MGCJ6
2025-05-29 18:13:47 INFO:debug_mgc_data.py:50:__main__:ConId: 706903676
2025-05-29 18:13:47 INFO:debug_mgc_data.py:51:__main__:LastTradeDateOrContractMonth: *************-05-29 18:13:47 INFO:debug_mgc_data.py:54:__main__:
--- 2. DETAILY KONTRAKTU ---
2025-05-29 18:13:47 INFO:debug_mgc_data.py:59:__main__:TimeZoneId: US/Eastern
2025-05-29 18:13:47 INFO:debug_mgc_data.py:60:__main__:TradingHours: 20250527:1800-20250528:1700;20250528:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700
2025-05-29 18:13:47 INFO:debug_mgc_data.py:61:__main__:LiquidHours: 20250528:0930-20250528:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700
2025-05-29 18:13:47 INFO:debug_mgc_data.py:66:__main__:
--- 3. AKTUÁLNE MARKET DATA ---
2025-05-29 18:13:47 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2119, reqId -1: Market data farm is connecting:usfuture
2025-05-29 18:13:48 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfuture
2025-05-29 18:13:49 INFO:debug_mgc_data.py:71:__main__:Last: 3452.5
2025-05-29 18:13:49 INFO:debug_mgc_data.py:72:__main__:Bid: 3456.9
2025-05-29 18:13:49 INFO:debug_mgc_data.py:73:__main__:Ask: 3458.5
2025-05-29 18:13:49 INFO:debug_mgc_data.py:74:__main__:Close: 3429.4
2025-05-29 18:13:49 INFO:debug_mgc_data.py:81:__main__:
--- 4. DENNÉ HISTORICKÉ DÁTA ---
2025-05-29 18:13:49 INFO:debug_mgc_data.py:83:__main__:Požadujem denné dáta s parametrami z hlavného skriptu:
2025-05-29 18:13:49 INFO:debug_mgc_data.py:84:__main__:  durationStr='5 D', barSizeSetting='1 day', whatToShow='ASK', useRTH=False
2025-05-29 18:13:50 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2106, reqId -1: HMDS data farm connection is OK:ushmds
2025-05-29 18:13:50 INFO:debug_mgc_data.py:96:__main__:Získal som 5 denných sviečok:
2025-05-29 18:13:50 ERROR:debug_mgc_data.py:147:__main__:Chyba pri získavaní denných dát: 'tzinfo' is an invalid keyword argument for replace()
2025-05-29 18:13:50 INFO:debug_mgc_data.py:150:__main__:
--- 6. HODINOVÉ DÁTA PRE POROVNANIE ---
2025-05-29 18:13:50 INFO:debug_mgc_data.py:162:__main__:Získal som 42 hodinových sviečok
2025-05-29 18:13:50 INFO:debug_mgc_data.py:163:__main__:Posledných 5 hodinových sviečok:
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 08:00 UTC | 2025-05-29 04:00 NY | O=3430.80 H=3448.10 L=3429.70 C=3447.20
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 09:00 UTC | 2025-05-29 05:00 NY | O=3447.20 H=3452.80 L=3441.20 C=3443.60
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 10:00 UTC | 2025-05-29 06:00 NY | O=3443.60 H=3450.60 L=3434.50 C=3440.20
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 11:00 UTC | 2025-05-29 07:00 NY | O=3440.20 H=3452.00 L=3439.70 C=3451.70
2025-05-29 18:13:50 INFO:debug_mgc_data.py:169:__main__:  2025-05-29 12:00 UTC | 2025-05-29 08:00 NY | O=3451.70 H=3459.40 L=3449.80 C=3458.30
2025-05-29 18:13:50 INFO:debug_mgc_data.py:177:__main__:
--- 7. KONTROLA REÁLNYCH TRŽNÝCH CIEN ---
2025-05-29 18:13:50 INFO:debug_mgc_data.py:178:__main__:Porovnajte získané ceny s:
2025-05-29 18:13:50 INFO:debug_mgc_data.py:179:__main__:  - CME Group: https://www.cmegroup.com/markets/metals/precious/gold.html
2025-05-29 18:13:50 INFO:debug_mgc_data.py:180:__main__:  - TradingView: https://www.tradingview.com/symbols/COMEX-GC1!/
2025-05-29 18:13:50 INFO:debug_mgc_data.py:181:__main__:  - Yahoo Finance: https://finance.yahoo.com/quote/GC%3DF/
2025-05-29 18:13:50 INFO:debug_mgc_data.py:185:__main__:
Aktuálny close z IB: 3458.30
2025-05-29 18:13:50 INFO:debug_mgc_data.py:186:__main__:Očakávaný rozsah pre zlato: 2300-2800 USD/oz
2025-05-29 18:13:50 WARNING:debug_mgc_data.py:189:__main__:⚠️  POZOR: Cena je mimo očakávaného rozsahu pre zlato!
2025-05-29 18:13:50 INFO:ib.py:290:ib_insync.ib:Disconnecting from 127.0.0.1:4001, 499 B sent in 14 messages, 29.4 kB received in 358 messages, session time 7.82 s.
2025-05-29 18:13:50 INFO:client.py:230:ib_insync.client:Disconnecting
2025-05-29 18:13:50 INFO:debug_mgc_data.py:199:__main__:Odpojený od IB
