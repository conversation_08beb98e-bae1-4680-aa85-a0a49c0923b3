#!/usr/bin/env python3
"""
Test dynamického výberu najlikvidnejšieho kontraktu pre každý nástroj
"""

import sys
import os
import logging
from datetime import datetime, timezone, timedelta
import pytz

# Pridaj cestu k modulom
sys.path.append('/root/PY')

from ib_insync import IB, Future

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(name)s:%(lineno)d:%(funcName)s:%(message)s',
    handlers=[
        logging.FileHandler('test_dynamic_contract_selection.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def get_most_liquid_contract(ib, symbol, exchange, currency='USD', days_before_expiry=30):
    """
    Vyberie najlikvidnejší (najbližší budúci) kontrakt pre daný symbol
    
    Args:
        ib: IB connection
        symbol: Symbol nástroja (napr. 'MGC', 'MES')
        exchange: Burza (napr. 'COMEX', 'CME')
        currency: Mena (default 'USD')
        days_before_expiry: Koľko dní pred expirációu rolluje na ďalší kontrakt
    
    Returns:
        Contract object alebo None
    """
    try:
        # Vytvor generický kontrakt
        generic_contract = Future(symbol=symbol, exchange=exchange, currency=currency)
        
        # Získaj všetky dostupné kontrakty
        contract_details = ib.reqContractDetails(generic_contract)
        
        if not contract_details:
            logger.error(f"[{symbol}] Nenašli sa žiadne kontrakty na {exchange}")
            return None
        
        logger.info(f"[{symbol}] Našiel som {len(contract_details)} kontraktov")
        
        # Aktuálny čas
        now = datetime.now(timezone.utc)
        rollover_threshold = now + timedelta(days=days_before_expiry)
        
        # Filtrovanie a triedenie kontraktov
        valid_contracts = []
        
        for cd in contract_details:
            contract = cd.contract
            
            # Parsuj dátum expirácie
            try:
                if hasattr(contract, 'lastTradeDateOrContractMonth') and contract.lastTradeDateOrContractMonth:
                    expiry_str = contract.lastTradeDateOrContractMonth
                    
                    # Formát môže byť YYYYMMDD alebo YYYYMM
                    if len(expiry_str) == 8:  # YYYYMMDD
                        expiry_date = datetime.strptime(expiry_str, '%Y%m%d').replace(tzinfo=timezone.utc)
                    elif len(expiry_str) == 6:  # YYYYMM
                        expiry_date = datetime.strptime(expiry_str + '01', '%Y%m%d').replace(tzinfo=timezone.utc)
                        # Pre YYYYMM formát, predpokladáme koniec mesiaca
                        if expiry_date.month == 12:
                            expiry_date = expiry_date.replace(year=expiry_date.year + 1, month=1, day=1) - timedelta(days=1)
                        else:
                            expiry_date = expiry_date.replace(month=expiry_date.month + 1, day=1) - timedelta(days=1)
                    else:
                        logger.warning(f"[{symbol}] Neznámy formát dátumu expirácie: {expiry_str}")
                        continue
                    
                    # Kontrola či kontrakt nie je expirovaný a nie je príliš blízko expirácie
                    if expiry_date > rollover_threshold:
                        valid_contracts.append({
                            'contract': contract,
                            'expiry_date': expiry_date,
                            'local_symbol': getattr(contract, 'localSymbol', ''),
                            'con_id': getattr(contract, 'conId', 0)
                        })
                        
                        logger.info(f"[{symbol}] Platný kontrakt: {contract.localSymbol} "
                                  f"(expiry: {expiry_date.strftime('%Y-%m-%d')}, "
                                  f"conId: {contract.conId})")
                    else:
                        logger.info(f"[{symbol}] Preskakujem kontrakt {contract.localSymbol} "
                                  f"(expiry: {expiry_date.strftime('%Y-%m-%d')} - príliš blízko expirácie)")
                        
            except Exception as e:
                logger.error(f"[{symbol}] Chyba pri parsovaní dátumu expirácie pre {contract}: {e}")
                continue
        
        if not valid_contracts:
            logger.error(f"[{symbol}] Nenašli sa žiadne platné kontrakty")
            return None
        
        # Zoraď podľa dátumu expirácie (najbližší najskôr)
        valid_contracts.sort(key=lambda x: x['expiry_date'])
        
        # Vyberi najbližší platný kontrakt
        selected = valid_contracts[0]
        logger.info(f"[{symbol}] VYBRANÝ KONTRAKT: {selected['local_symbol']} "
                   f"(expiry: {selected['expiry_date'].strftime('%Y-%m-%d')}, "
                   f"conId: {selected['con_id']})")
        
        return selected['contract']
        
    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri výbere najlikvidnejšieho kontraktu: {e}")
        return None

def test_all_instruments():
    """Test dynamického výberu kontraktov pre všetky nástroje"""
    
    # Pripoj sa k IB
    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=998)
        logger.info("Úspešne pripojený k IB")
        
        # Definuj nástroje na testovanie
        instruments = [
            {'symbol': 'MGC', 'exchange': 'COMEX'},
            {'symbol': 'MES', 'exchange': 'CME'},
            {'symbol': 'M2K', 'exchange': 'CME'},
            {'symbol': 'MNQ', 'exchange': 'CME'},
            {'symbol': 'M6A', 'exchange': 'CME'},
            {'symbol': 'M6B', 'exchange': 'CME'},
        ]
        
        logger.info("\n=== TEST DYNAMICKÉHO VÝBERU KONTRAKTOV ===")
        
        results = {}
        
        for instrument in instruments:
            symbol = instrument['symbol']
            exchange = instrument['exchange']
            
            logger.info(f"\n--- Testovanie {symbol} na {exchange} ---")
            
            # Test s rôznymi rollover pravidlami
            for days_before in [30, 15, 7]:
                logger.info(f"\nTest s rollover {days_before} dní pred expirációu:")
                
                contract = get_most_liquid_contract(
                    ib, symbol, exchange, 
                    days_before_expiry=days_before
                )
                
                if contract:
                    results[f"{symbol}_{days_before}d"] = {
                        'symbol': symbol,
                        'local_symbol': contract.localSymbol,
                        'expiry': contract.lastTradeDateOrContractMonth,
                        'con_id': contract.conId,
                        'days_before': days_before
                    }
                    
                    logger.info(f"  ✓ Výsledok: {contract.localSymbol} "
                              f"(expiry: {contract.lastTradeDateOrContractMonth})")
                else:
                    logger.error(f"  ✗ Nepodarilo sa nájsť kontrakt pre {symbol}")
        
        # Súhrn výsledkov
        logger.info("\n=== SÚHRN VÝSLEDKOV ===")
        
        for key, result in results.items():
            logger.info(f"{result['symbol']} ({result['days_before']}d rollover): "
                       f"{result['local_symbol']} -> {result['expiry']}")
        
        # Porovnanie s aktuálnou logikou
        logger.info("\n=== POROVNANIE S AKTUÁLNOU LOGIKOU ===")
        
        # Aktuálna logika vracia 202506 (jún 2025) pre všetky nástroje
        current_logic_month = "202506"
        
        for instrument in instruments:
            symbol = instrument['symbol']
            
            # Nájdi výsledok s 30 dňami (štandardné nastavenie)
            key = f"{symbol}_30d"
            if key in results:
                new_expiry = results[key]['expiry']
                logger.info(f"{symbol}:")
                logger.info(f"  Aktuálna logika: {current_logic_month}")
                logger.info(f"  Nová logika:     {new_expiry}")
                
                if new_expiry != current_logic_month:
                    logger.info(f"  >>> ZMENA! Nová logika navrhuje iný kontrakt <<<")
                else:
                    logger.info(f"  = Zhoduje sa")
        
    except Exception as e:
        logger.error(f"Chyba pri teste: {e}")
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == "__main__":
    logger.info("=== ŠTART TESTU DYNAMICKÉHO VÝBERU KONTRAKTOV ===")
    test_all_instruments()
    logger.info("=== KONIEC TESTU ===")
