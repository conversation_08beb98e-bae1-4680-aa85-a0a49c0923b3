#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import sys
import os
from datetime import datetime, timezone, timedelta
import pytz
from ib_insync import IB, Future, util
import pandas as pd

# Pridaj PY script adresár do cesty
sys.path.append(os.path.join(os.path.dirname(__file__), 'PY script'))

try:
    import config
    import utils
except ImportError as e:
    print(f"Chyba pri importovaní modulov: {e}")
    sys.exit(1)

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(filename)s:%(lineno)d:%(name)s:%(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_all_instruments():
    """Test výberu kontraktov a cien pre všetky obchodované inštrumenty"""
    
    ib = IB()
    
    try:
        logger.info("=== TEST VŠETKÝCH INŠTRUMENTOV ===")
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=997)
        logger.info("Úspešne pripojený k IB")
        
        # Zoznam všetkých inštrumentov z config
        instruments = config.INSTRUMENTS_CONFIG
        
        results = {}
        
        for inst in instruments:
            symbol = inst['symbol']
            exchange = inst['exchange']
            currency = inst['currency']
            
            logger.info(f"\n{'='*60}")
            logger.info(f"TESTOVANIE: {symbol} na {exchange}")
            logger.info(f"{'='*60}")
            
            try:
                # 1. Získaj najlepší kontrakt
                contract = utils.get_best_contract_for_instrument(ib, symbol, exchange, currency)
                
                if not contract:
                    logger.error(f"[{symbol}] Nepodarilo sa získať kontrakt!")
                    results[symbol] = {'status': 'CHYBA', 'error': 'Žiadny kontrakt'}
                    continue
                
                logger.info(f"[{symbol}] Vybraný kontrakt: {contract.localSymbol}")
                logger.info(f"[{symbol}] ConId: {contract.conId}")
                logger.info(f"[{symbol}] Expirácia: {contract.lastTradeDateOrContractMonth}")
                
                # 2. Získaj aktuálne market data
                logger.info(f"\n--- AKTUÁLNE CENY PRE {symbol} ---")
                try:
                    ticker = ib.reqMktData(contract, '', False, False)
                    ib.sleep(2)
                    
                    last_price = ticker.last if ticker.last is not None else "N/A"
                    bid_price = ticker.bid if ticker.bid is not None else "N/A"
                    ask_price = ticker.ask if ticker.ask is not None else "N/A"
                    close_price = ticker.close if ticker.close is not None else "N/A"
                    
                    logger.info(f"[{symbol}] Last: {last_price}")
                    logger.info(f"[{symbol}] Bid: {bid_price}")
                    logger.info(f"[{symbol}] Ask: {ask_price}")
                    logger.info(f"[{symbol}] Close: {close_price}")
                    
                    ib.cancelMktData(contract)
                    
                    # Kontrola rozumnosti cien
                    price_check = "OK"
                    if symbol == 'MGC' and isinstance(last_price, (int, float)):
                        if last_price < 2000 or last_price > 3000:
                            price_check = "PODOZRIVÉ - mimo rozsahu 2000-3000"
                    elif symbol in ['MES', 'MNQ', 'M2K'] and isinstance(last_price, (int, float)):
                        if last_price < 1000 or last_price > 50000:
                            price_check = "PODOZRIVÉ - mimo rozumného rozsahu"
                    elif symbol in ['M6A', 'M6B', 'M6E'] and isinstance(last_price, (int, float)):
                        if last_price < 0.5 or last_price > 2.0:
                            price_check = "PODOZRIVÉ - mimo rozsahu 0.5-2.0"
                    
                    logger.info(f"[{symbol}] Kontrola cien: {price_check}")
                    
                except Exception as e:
                    logger.error(f"[{symbol}] Chyba pri získavaní market data: {e}")
                    last_price = "CHYBA"
                    price_check = "CHYBA"
                
                # 3. Test historických dát
                logger.info(f"\n--- HISTORICKÉ DÁTA PRE {symbol} ---")
                try:
                    # Denné dáta
                    daily_bars = ib.reqHistoricalData(
                        contract, 
                        '', 
                        '5 D', 
                        '1 day', 
                        'ASK', 
                        False, 
                        formatDate=1
                    )
                    
                    logger.info(f"[{symbol}] Získal som {len(daily_bars)} denných sviečok")
                    
                    if daily_bars:
                        latest_daily = daily_bars[-1]
                        logger.info(f"[{symbol}] Posledná denná sviečka: "
                                  f"O={latest_daily.open:.2f} H={latest_daily.high:.2f} "
                                  f"L={latest_daily.low:.2f} C={latest_daily.close:.2f}")
                        
                        # Test Camarilla pivotov
                        if len(daily_bars) >= 2:
                            df = util.df(daily_bars)
                            h4, l4 = utils.calc_pivots(df, symbol)
                            if h4 is not None and l4 is not None:
                                logger.info(f"[{symbol}] Camarilla pivoty: H4={h4:.2f}, L4={l4:.2f}")
                            else:
                                logger.warning(f"[{symbol}] Nepodarilo sa vypočítať Camarilla pivoty")
                    
                    # Hodinové dáta
                    hourly_bars = ib.reqHistoricalData(
                        contract,
                        '',
                        '1 D',
                        '1 hour',
                        'ASK',
                        False,
                        formatDate=1
                    )
                    
                    logger.info(f"[{symbol}] Získal som {len(hourly_bars)} hodinových sviečok")
                    
                    if hourly_bars:
                        latest_hourly = hourly_bars[-1]
                        logger.info(f"[{symbol}] Posledná hodinová sviečka: "
                                  f"O={latest_hourly.open:.2f} H={latest_hourly.high:.2f} "
                                  f"L={latest_hourly.low:.2f} C={latest_hourly.close:.2f}")
                    
                    data_status = "OK"
                    
                except Exception as e:
                    logger.error(f"[{symbol}] Chyba pri získavaní historických dát: {e}")
                    data_status = "CHYBA"
                
                # Ulož výsledky
                results[symbol] = {
                    'status': 'OK',
                    'contract': contract.localSymbol,
                    'conId': contract.conId,
                    'expiry': contract.lastTradeDateOrContractMonth,
                    'last_price': last_price,
                    'price_check': price_check,
                    'data_status': data_status
                }
                
            except Exception as e:
                logger.error(f"[{symbol}] Kritická chyba: {e}", exc_info=True)
                results[symbol] = {'status': 'KRITICKÁ CHYBA', 'error': str(e)}
        
        # Súhrn výsledkov
        logger.info(f"\n{'='*80}")
        logger.info("SÚHRN VÝSLEDKOV")
        logger.info(f"{'='*80}")
        
        for symbol, result in results.items():
            if result['status'] == 'OK':
                logger.info(f"{symbol:>6}: {result['contract']:>10} | "
                          f"Cena: {str(result['last_price']):>8} | "
                          f"Kontrola: {result['price_check']:>20} | "
                          f"Dáta: {result['data_status']}")
            else:
                logger.error(f"{symbol:>6}: {result['status']} - {result.get('error', 'Neznáma chyba')}")
        
        logger.info(f"\n{'='*80}")
        logger.info("ODPORÚČANIA:")
        logger.info(f"{'='*80}")
        
        problem_instruments = []
        for symbol, result in results.items():
            if result['status'] != 'OK':
                problem_instruments.append(symbol)
            elif result.get('price_check') and 'PODOZRIVÉ' in result['price_check']:
                problem_instruments.append(f"{symbol} (podozrivé ceny)")
            elif result.get('data_status') == 'CHYBA':
                problem_instruments.append(f"{symbol} (problém s dátami)")
        
        if problem_instruments:
            logger.warning("Inštrumenty s problémami:")
            for prob in problem_instruments:
                logger.warning(f"  - {prob}")
        else:
            logger.info("✅ Všetky inštrumenty fungujú správne!")
                
    except Exception as e:
        logger.error(f"Kritická chyba v teste: {e}", exc_info=True)
        
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == '__main__':
    test_all_instruments()
