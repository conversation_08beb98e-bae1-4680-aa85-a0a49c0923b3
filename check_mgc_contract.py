#!/usr/bin/env python3
"""
Diagnostický skript na kontrolu MGC kontraktov
Skontroluje aký kontrakt používa get_front_contract_month() pre MGC
a porovná s aktuálnymi dostupnými kontraktami
"""

import sys
import os
import logging
from datetime import datetime, timezone
import pytz

# Pridaj cestu k modulom
sys.path.append('/root/PY')

from ib_insync import IB, Future
from utils import get_front_contract_month

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(name)s:%(lineno)d:%(funcName)s:%(message)s',
    handlers=[
        logging.FileHandler('check_mgc_contract.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def check_mgc_contracts():
    """Skontroluje MGC kontrakty"""
    
    # Pripoj sa k IB
    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect('127.0.0.1', 4001, clientId=999)
        logger.info("Úspešne pripojený k IB")
        
        # Aktuálny čas
        now = datetime.now(timezone.utc)
        logger.info(f"Aktuálny čas UTC: {now}")
        
        # 1. Skontroluj aký kontrakt vracia get_front_contract_month pre MGC
        logger.info("\n=== KONTROLA get_front_contract_month() PRE MGC ===")
        
        try:
            mgc_contract_month = get_front_contract_month('MGC', now)
            logger.info(f"get_front_contract_month('MGC') vrátil: {mgc_contract_month}")
            
            # Vytvor kontrakt
            mgc_contract = Future(
                symbol='MGC',
                lastTradeDateOrContractMonth=mgc_contract_month,
                exchange='COMEX',
                currency='USD'
            )
            
            # Kvalifikuj kontrakt
            qualified_contracts = ib.qualifyContracts(mgc_contract)
            if qualified_contracts:
                mgc_qualified = qualified_contracts[0]
                logger.info(f"Kvalifikovaný MGC kontrakt:")
                logger.info(f"  conId: {mgc_qualified.conId}")
                logger.info(f"  symbol: {mgc_qualified.symbol}")
                logger.info(f"  lastTradeDateOrContractMonth: {mgc_qualified.lastTradeDateOrContractMonth}")
                logger.info(f"  localSymbol: {mgc_qualified.localSymbol}")
                logger.info(f"  tradingClass: {mgc_qualified.tradingClass}")
                logger.info(f"  multiplier: {mgc_qualified.multiplier}")
                logger.info(f"  exchange: {mgc_qualified.exchange}")
                
                # Skús získať detaily kontraktu
                try:
                    contract_details = ib.reqContractDetails(mgc_qualified)
                    if contract_details:
                        detail = contract_details[0]
                        logger.info(f"  Detaily kontraktu:")
                        logger.info(f"    lastTradeDateOrContractMonth: {detail.contract.lastTradeDateOrContractMonth}")
                        logger.info(f"    timeZoneId: {detail.timeZoneId}")
                        logger.info(f"    tradingHours: {detail.tradingHours}")
                        logger.info(f"    liquidHours: {detail.liquidHours}")
                        
                        # Skontroluj či je kontrakt aktívny
                        if hasattr(detail, 'contractMonth'):
                            logger.info(f"    contractMonth: {detail.contractMonth}")
                        if hasattr(detail, 'lastTradeDate'):
                            logger.info(f"    lastTradeDate: {detail.lastTradeDate}")
                            
                except Exception as e:
                    logger.error(f"Chyba pri získavaní detailov kontraktu: {e}")
                    
            else:
                logger.error("Nepodarilo sa kvalifikovať MGC kontrakt!")
                
        except Exception as e:
            logger.error(f"Chyba pri get_front_contract_month('MGC'): {e}")
        
        # 2. Skontroluj dostupné MGC kontrakty
        logger.info("\n=== KONTROLA DOSTUPNÝCH MGC KONTRAKTOV ===")
        
        try:
            # Vytvor generický MGC kontrakt
            generic_mgc = Future(symbol='MGC', exchange='COMEX', currency='USD')
            
            # Získaj všetky dostupné kontrakty
            all_mgc_contracts = ib.reqContractDetails(generic_mgc)
            
            logger.info(f"Našiel som {len(all_mgc_contracts)} MGC kontraktov:")
            
            for i, contract_detail in enumerate(all_mgc_contracts[:10]):  # Zobraz prvých 10
                contract = contract_detail.contract
                logger.info(f"  {i+1}. conId: {contract.conId}")
                logger.info(f"     symbol: {contract.symbol}")
                logger.info(f"     lastTradeDateOrContractMonth: {contract.lastTradeDateOrContractMonth}")
                logger.info(f"     localSymbol: {contract.localSymbol}")
                logger.info(f"     multiplier: {contract.multiplier}")
                
                # Skontroluj či je to aktuálny front month
                if contract.localSymbol.endswith('M5'):  # Jún 2025
                    logger.info(f"     >>> TOTO JE JÚN 2025 KONTRAKT (M5) <<<")
                elif contract.localSymbol.endswith('N5'):  # Júl 2025
                    logger.info(f"     >>> TOTO JE JÚL 2025 KONTRAKT (N5) <<<")
                elif contract.localSymbol.endswith('Q5'):  # August 2025
                    logger.info(f"     >>> TOTO JE AUGUST 2025 KONTRAKT (Q5) <<<")
                    
                logger.info("")
                
        except Exception as e:
            logger.error(f"Chyba pri získavaní dostupných MGC kontraktov: {e}")
        
        # 3. Porovnaj s inými nástrojmi
        logger.info("\n=== POROVNANIE S INÝMI NÁSTROJMI ===")
        
        instruments = ['M2K', 'MES', 'MNQ']
        for instrument in instruments:
            try:
                contract_month = get_front_contract_month(instrument, now)
                logger.info(f"get_front_contract_month('{instrument}') vrátil: {contract_month}")
            except Exception as e:
                logger.error(f"Chyba pri get_front_contract_month('{instrument}'): {e}")
        
        # 4. Test historických dát pre MGC
        logger.info("\n=== TEST HISTORICKÝCH DÁT PRE MGC ===")
        
        if 'mgc_qualified' in locals():
            try:
                # Simuluj presne tú istú požiadavku ako hlavný skript
                end_datetime = "20250528 23:59:59 UTC"
                logger.info(f"Požadujem historické dáta pre MGC s endDateTime: {end_datetime}")
                
                bars = ib.reqHistoricalData(
                    mgc_qualified,
                    endDateTime=end_datetime,
                    durationStr='1 D',
                    barSizeSetting='1 hour',
                    whatToShow='ASK',
                    useRTH=False,
                    formatDate=1
                )
                
                logger.info(f"Získal som {len(bars)} sviečok:")
                for bar in bars:
                    # Konvertuj čas na NY time pre lepšie pochopenie
                    utc_time = bar.date.replace(tzinfo=timezone.utc)
                    ny_time = utc_time.astimezone(pytz.timezone('America/New_York'))
                    
                    logger.info(f"  {utc_time.strftime('%Y-%m-%d %H:%M')} UTC | "
                              f"{ny_time.strftime('%Y-%m-%d %H:%M')} NY | "
                              f"O={bar.open:.2f} H={bar.high:.2f} L={bar.low:.2f} C={bar.close:.2f}")
                
            except Exception as e:
                logger.error(f"Chyba pri získavaní historických dát: {e}")
        
    except Exception as e:
        logger.error(f"Chyba pri pripojení k IB: {e}")
    finally:
        if ib.isConnected():
            ib.disconnect()
            logger.info("Odpojený od IB")

if __name__ == "__main__":
    logger.info("=== ŠTART KONTROLY MGC KONTRAKTOV ===")
    check_mgc_contracts()
    logger.info("=== KONIEC KONTROLY MGC KONTRAKTOV ===")
