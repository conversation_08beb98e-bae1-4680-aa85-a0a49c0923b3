
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <title>VPS Trading Dokumentácia</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; background-color: #fff; color: #222; }
        h1 { color: #2a4b8d; }
        h2 { color: #375a7f; margin-top: 30px; }
        p, pre { margin: 10px 0; }
        pre { background-color: #f4f4f4; padding: 10px; border-left: 3px solid #ccc; overflow-x: auto; }
    </style>
</head>
<body>
<h1>Dokumentácia k VPS Trading Prostrediu (Hetzner)</h1>
<p>Dokumentácia k VPS Trading Prostrediu (Hetzner)</p><h2>1. Základné informácie o VPS</h2><p>- Host name: ubuntu-4gb-fsn1-2
- Distribúcia: Ubuntu 24.04.2 LTS (noble)
- Uptime: 2 dni, 2:49
- Používateľ: trader
- Verejná IP adresa: ************</p><h2>2. IBGateway</h2><p>- Verzia: 1030
- Inštalačná cesta: /home/<USER>/Jts/ibgateway/1030
- Beží pod používateľom: trader
- Spúšťa sa cez xvfb-run + Java
- Systemd jednotka (ibgateway.service):</p><pre>[Unit]
Description=IB Gateway (headless)
After=network.target

[Service]
Type=simple
User=trader
WorkingDirectory=/home/<USER>/Jts/ibgateway/1030

Environment=XDG_RUNTIME_DIR=/tmp/ibgateway-runtime-trader
Environment=MALLOC_ARENA_MAX=1
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

ExecStartPre=/usr/bin/mkdir -p /tmp/ibgateway-runtime-trader
ExecStartPre=/usr/bin/chown trader:trader /tmp/ibgateway-runtime-trader

ExecStart=/usr/bin/xvfb-run -a -s "-screen 0 1920x1080x24" /home/<USER>/Jts/ibgateway/1030/ibgateway -J-Djava.net.preferIPv4Stack=true

Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target</pre><p>- Konfiguračný súbor jts.xml: obsahuje AutoReconnect a port 4001</p><p>- Súbory login.cfg a jts.ini neexistujú</p><p>- Adresár s logmi neexistuje</p><h2>3. Python bot (mes_bot)</h2><p>- Cesta: /home/<USER>/mes_bot
- Hlavný skript: camarilla.py
- Logovanie: trades_log.csv + Telegram (v rámci camarilla.py)
- Virtualenv: venv/
- Systemd služba: mesbot.service</p><pre>[Unit]
Description=MES Camarilla Bot
Requires=ibgateway.service
After=network.target ibgateway.service

[Service]
Type=simple
User=trader
WorkingDirectory=/home/<USER>/mes_bot

ExecStart=/home/<USER>/mes_bot/venv/bin/python /home/<USER>/mes_bot/camarilla.py
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target</pre><p>- Cron job: každých 5 minút sa spúšťa check_ib_status.sh</p><h2>4. Log obchodov (CSV)</h2><p>- Súbor: /home/<USER>/mes_bot/trades_log.csv
- Prvé riadky:

symbol,signal,entry_price,exit_price,pnl,entry_time,sl_level,trail_activated
MES,LONG,5663.5,5663.5,0.0,2025-05-07 06:30:01,5593.5,False</p><h2>5. VNC pripojenie</h2><p>- Klient: TigerVNC na Windows
- Server: Xtigervnc beží na :2 (port 5902)
- SSH tunel: manuálne cez

  ssh -L 5902:localhost:5902 trader@************
- Pripojenie vo VNC: localhost:2</p>
<h2>6. Konfigurácia IBGateway (jts.xml)</h2>
<p><strong>Súbor:</strong> <code>/home/<USER>/Jts/ibgateway/1030/jts.xml</code> Tento XML súbor definuje automatické opätovné pripojenie a správanie API IBGateway.</p>
<pre><code>&lt;?xml version="1.0" encoding="UTF-8"?&gt;
&lt;Gateway&gt;
  &lt;Logon&gt;
    &lt;AutoReconnect&gt;
      &lt;enabled&gt;true&lt;/enabled&gt;
      &lt;reconnectIntervalSeconds&gt;5&lt;/reconnectIntervalSeconds&gt;
      &lt;maxReconnectAttempts&gt;9999&lt;/maxReconnectAttempts&gt;
    &lt;/AutoReconnect&gt;
  &lt;/Logon&gt;
  &lt;API&gt;
    &lt;Settings&gt;
      &lt;SocketPort&gt;4001&lt;/SocketPort&gt;
      &lt;AllowConnectionsFromLocalHostOnly&gt;true&lt;/AllowConnectionsFromLocalHostOnly&gt;
      &lt;TrustedIps/&gt;
    &lt;/Settings&gt;
  &lt;/API&gt;
&lt;/Gateway&gt;
</code></pre>
<p><strong>Poznámka:</strong> Táto hodnota bola upravená na <code>true</code>, čo zabezpečuje, že pripojenie k API IBGateway (port 4001) je možné iba z lokálneho systému. V kombinácii s blokovaním portu 4001 cez UFW je to bezpečné riešenie pre bota bežiaceho na tom istom VPS.</p>
<h2>7. Firewall (ufw) – zabezpečenie portov</h2><p>Na VPS bol aktivovaný jednoduchý firewall pomocou nástroja UFW (Uncomplicated Firewall), ktorý obmedzuje sieťové prístupy a chráni bežiace služby pred vonkajšími útokmi.</p><p>Povolené porty:
- 22 (SSH)
- 5902 (VNC prístup cez tunelovanie)

Zablokovaný port:
- 4001 (API port IBGateway – ako doplnková ochrana, hoci AllowConnectionsFromLocalHostOnly=true už bráni externým prístupom)</p><p>Príkazy použité na nastavenie firewallu:</p><pre>sudo ufw allow 22
sudo ufw allow 5902
sudo ufw deny 4001</pre><p>Poznámka: Tieto pravidlá zaisťujú, že VPS je prístupný len cez SSH a VNC (ak je tunelovaný). Prístup na IB API port (4001) je výslovne zablokovaný na sieti aj v IBGateway konfigurácii.</p><p>Aktuálny stav UFW:
- Firewall je aktívny
- Povolené porty: 22 (SSH), 5902 (VNC)
- Port 4001 (IBGateway API) je zablokovaný
- Ostatné pokusy o povolenie portov (napr. 5901 pre konkrétnu IP) boli neplatné alebo odstránené

Overené príkazom:
sudo ufw status verbose

Výstup ukazuje, že pravidlá sú aktívne a bezpečne nastavené.</p>

<h2>8. Telegram notifikácie pri výpadku IBGateway</h2>
<p>Telegram notifikácie neposiela hlavný skript bota, ale samostatný monitorovací systém pozostávajúci z dvoch súborov:</p>
<ul>
  <li><code>/home/<USER>/check_ib_connection.py</code> – kontroluje pripojenie k IB API cez <code>ib_insync</code></li>
  <li><code>/home/<USER>/check_ib_status.sh</code> – vykonáva komplexnú kontrolu a odosiela Telegram správu</li>
</ul>
<p>Monitorovací skript <code>check_ib_status.sh</code> je spúšťaný každých 5 minút cez <code>cron</code> a vykonáva nasledovné:</p>
<ul>
  <li>kontroluje, či beží port <strong>4001</strong></li>
  <li>kontroluje, či beží systemd služba <code>mesbot.service</code></li>
  <li>kontroluje, či existuje proces <code>ibgateway</code></li>
  <li>volá <code>check_ib_connection.py</code> a sleduje, či API funguje</li>
</ul>
<p>Ak sa zistí výpadok API spojenia, skript vytvorí dočasný súbor <code>/tmp/ib_status.tmp</code> a odošle správu na Telegram.</p>
<p>Ak sa API spojenie neskôr obnoví, skript vymaže súbor a odošle správu o obnovení spojenia.</p>
<p>Token a ID sú bezpečne uložené priamo v skripte (token bol v dokumentácii anonymizovaný).</p>


<p>Notifikácie na Telegram nie sú súčasťou hlavného skriptu bota <code>camarilla.py</code>, ale pravdepodobne ich zabezpečuje externý skript <code>check_ib_status.sh</code>, ktorý je spúšťaný cez <code>cron</code>.</p>
<p>V prípade výpadku alebo prerušenia relácie IBGateway je odoslaná notifikácia na Telegram.</p>
<p>Tento skript zrejme deteguje prerušenie IBGateway relácie a následne odošle upozornenie na Telegram.</p>

</body>
</html>

<h2>9. Pripojenie cez VNC (SSH tunelovanie)</h2>
<p>Pre bezpečný prístup ku grafickému rozhraniu na VPS je použitý SSH tunel. VNC server beží na VPS ako <code>:1</code>, teda port <code>5901</code>.</p>
<p>Na lokálnom počítači (Windows) treba spustiť nasledovný príkaz v termináli:</p>
<pre>ssh -L 5901:localhost:5901 trader@************</pre>
<p>Potom sa v RealVNC Viewer pripojíš na <strong>localhost:1</strong>.</p>
<p>Poznámka: Ak si omylom spustil tunel priamo na VPS, treba ho ukončiť pomocou <code>Ctrl+C</code> alebo <code>kill &lt;PID&gt;</code> a vytvoriť ho znova na svojom lokálnom PC.</p>
