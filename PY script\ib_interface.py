# ib_interface.py

import logging
import time
from ib_insync import Future, MarketOrder, Order, Contract
import pandas as pd

import config
from utils import send_telegram

logger = logging.getLogger(__name__)

def fetch_ib_contract(ib_conn, symbol: str, exchange: str, expiry_month: str, sec_type: str, currency: str) -> Contract | None:
    try:
        if sec_type != 'FUTURE':
            logger.error(f"[{symbol}] Skript podporuje len secType='FUT', zadané: {sec_type}.")
            return None
        contract_details_req = Future(symbol=symbol, exchange=exchange, currency=currency)

        cds = ib_conn.reqContractDetails(contract_details_req)
        if not cds:
            logger.error(f"[{symbol}] Nenašli sa žiadne detaily kontraktov pre {symbol} na {exchange}.")
            return None

        logger.debug(f"[{symbol}] Nájdených {len(cds)} detailov kontraktov. Filtrujem pre expiráciu {expiry_month}...")
        matched_contracts = []
        for cd_item in cds:
            contract = cd_item.contract
            if hasattr(contract, 'lastTradeDateOrContractMonth') and \
               contract.lastTradeDateOrContractMonth and \
               contract.lastTradeDateOrContractMonth.startswith(expiry_month):
                logger.debug(f"[{symbol}] Kandidát: {getattr(contract, 'localSymbol', symbol)} s expiraciou {contract.lastTradeDateOrContractMonth}")
                matched_contracts.append(contract)

        if not matched_contracts:
            logger.warning(f"[{symbol}] Presný kontrakt pre expiráciu {expiry_month} (startswith) nebol nájdený. Skúšam širšiu zhodu (obsahuje rok+mesiac)...")
            for cd_item in cds:
                contract = cd_item.contract
                if hasattr(contract, 'lastTradeDateOrContractMonth') and \
                   contract.lastTradeDateOrContractMonth and expiry_month[:6] in contract.lastTradeDateOrContractMonth:
                    logger.info(f"[{symbol}] Používam širšiu zhodu, nájdený kontrakt: {getattr(contract, 'localSymbol', symbol)} ({contract.lastTradeDateOrContractMonth})")
                    return contract
            logger.error(f"[{symbol}] Ani širšie vyhľadávanie nenašlo kontrakt pre expiráciu {expiry_month}.")
            return None

        matched_contracts.sort(key=lambda c: c.lastTradeDateOrContractMonth)
        selected_contract = matched_contracts[0]
        logger.info(f"[{symbol}] Nájdený a vybraný kontrakt: {getattr(selected_contract, 'localSymbol', symbol)} (Expirácia: {selected_contract.lastTradeDateOrContractMonth})")
        return selected_contract

    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri sťahovaní detailov kontraktu: {e}", exc_info=True)
        return None # Explicitne None

def place_bracket_order(ib_conn, instrument_data: dict, signal_type: str, ref_entry_price: float) -> bool:
    symbol = instrument_data['symbol']
    contract_obj = instrument_data.get('contract')
    if not contract_obj or not isinstance(contract_obj, Contract):
        logger.error(f"[{symbol}] Chýba platný objekt kontraktu. Príkaz sa neumiestňuje.")
        return False

    action = 'BUY' if signal_type == 'LONG' else 'SELL'
    tick_size_val = config.TICK_SIZES.get(symbol)
    if tick_size_val is None:
        logger.error(f"[{symbol}] Tick size nie je definovaný v config.TICK_SIZES! Príkaz sa neumiestňuje.")
        return False

    sl_pts_config = getattr(config, f"SL_PTS_{signal_type.upper()}", 70)
    trail_pts_config = getattr(config, f"TRAIL_PTS_{signal_type.upper()}", 40)
    trail_offset_config = getattr(config, f"TRAIL_OFFSET_{signal_type.upper()}", 1)

    sl_price_difference = sl_pts_config * tick_size_val
    trail_activation_price_difference = trail_pts_config * tick_size_val
    trail_offset_as_price = trail_offset_config * tick_size_val

    logger.info(f"[{symbol}] Používa sa tick size {tick_size_val}. SL diff: {sl_price_difference:.5f}, Trail act. diff: {trail_activation_price_difference:.5f}, Trail offset (price): {trail_offset_as_price:.5f}")

    parent_order_id_local = None
    sl_order_id_final = None
    parent_market_order_obj = None

    try:
        parent_market_order_obj = MarketOrder(action, config.QUANTITY)
        parent_market_order_obj.transmit = False
        parent_market_order_obj.outsideRth = True

        trade_submission_details = ib_conn.placeOrder(contract_obj, parent_market_order_obj)
        ib_conn.sleep(0.5)

        parent_order_id_retrieved = False
        if parent_market_order_obj.orderId and parent_market_order_obj.orderId != 0 :
            parent_order_id_local = parent_market_order_obj.orderId
            parent_order_id_retrieved = True
        elif hasattr(trade_submission_details, 'order') and trade_submission_details.order and \
             hasattr(trade_submission_details.order, 'orderId') and trade_submission_details.order.orderId and \
             trade_submission_details.order.orderId != 0:
             parent_order_id_local = trade_submission_details.order.orderId
             parent_order_id_retrieved = True

        if not parent_order_id_retrieved:
            logger.debug(f"[{symbol}] Čakám na Parent Order ID (max {config.ORDER_ID_WAIT_SECONDS}s)...")
            for i in range(getattr(config, 'ORDER_ID_WAIT_SECONDS', 10) -1): # -1 lebo sme už pol sekundy čakali
                if parent_market_order_obj.orderId and parent_market_order_obj.orderId !=0:
                    parent_order_id_local = parent_market_order_obj.orderId
                    parent_order_id_retrieved = True; break
                logger.debug(f"[{symbol}] Parent Order ID stále nie je, čakám... ({i+1})")
                ib_conn.sleep(1)

        if not parent_order_id_retrieved:
            logger.error(f"[{symbol}] Nepodarilo sa získať orderId pre parent order po {config.ORDER_ID_WAIT_SECONDS}s.")
            return False

        logger.info(f"[{symbol}] Parent order ID: {parent_order_id_local}, Stav: {trade_submission_details.orderStatus.status if trade_submission_details and trade_submission_details.orderStatus else 'N/A'}")

        calculated_sl_price = ref_entry_price - sl_price_difference if signal_type == 'LONG' else ref_entry_price + sl_price_difference

        num_decimals = 0
        str_tick_size = str(tick_size_val)
        if '.' in str_tick_size: num_decimals = len(str_tick_size.split('.')[1])

        if num_decimals == 0:
            if tick_size_val < 1 and '.' not in str_tick_size :
                num_decimals = 2
            elif tick_size_val >=1 :
                 num_decimals = 2
        if num_decimals > 8: num_decimals = 8

        final_sl_price = round(calculated_sl_price, num_decimals)
        logger.info(f"[{symbol}] Vypočítaná SL cena: {calculated_sl_price}, Zaokrúhlená SL cena: {final_sl_price} (des. miesta použité: {num_decimals})")

        stop_loss_order_obj = Order(
            orderType='STP', action=('SELL' if signal_type == 'LONG' else 'BUY'),
            totalQuantity=config.QUANTITY, auxPrice=final_sl_price,
            parentId=parent_order_id_local, transmit=True
        )
        stop_loss_order_obj.outsideRth = True

        sl_trade_submission_details = ib_conn.placeOrder(contract_obj, stop_loss_order_obj)
        ib_conn.sleep(0.5)

        sl_order_id_retrieved = False
        if stop_loss_order_obj.orderId and stop_loss_order_obj.orderId != 0:
            sl_order_id_final = stop_loss_order_obj.orderId
            sl_order_id_retrieved = True
        else:
            logger.debug(f"[{symbol}] Čakám na SL Order ID...")
            for i in range(getattr(config, 'ORDER_ID_WAIT_SECONDS', 10) -1):
                if stop_loss_order_obj.orderId and stop_loss_order_obj.orderId != 0:
                    sl_order_id_final = stop_loss_order_obj.orderId
                    sl_order_id_retrieved = True; break
                logger.debug(f"[{symbol}] SL Order ID stále nie je, čakám... ({i+1})")
                ib_conn.sleep(1)

        if not sl_order_id_retrieved:
            logger.error(f"[{symbol}] Nepodarilo sa získať orderId pre SL order. Parent order {parent_order_id_local} môže byť aktívny bez SL!")
            logger.warning(f"[{symbol}] Pokus o zrušenie parent orderu {parent_order_id_local} kvôli zlyhaniu SL.")
            try:
                # Zrušíme pôvodný objekt parent príkazu, ak existuje a má orderId
                if parent_market_order_obj and parent_market_order_obj.orderId == parent_order_id_local:
                    ib_conn.cancelOrder(parent_market_order_obj)
                    logger.info(f"[{symbol}] Parent order (objekt, ID {parent_order_id_local}) zrušený (kvôli zlyhaniu SL).")
                else: # Ak nemáme istotu, vytvoríme nový Order objekt len s ID
                    temp_cancel_order_sl_fail = Order(orderId=parent_order_id_local)
                    ib_conn.cancelOrder(temp_cancel_order_sl_fail)
                    logger.info(f"[{symbol}] Parent order (nový objekt, ID {parent_order_id_local}) zrušený (kvôli zlyhaniu SL).")

            except Exception as e_cancel_p_on_sl_fail:
                logger.error(f"[{symbol}] Nepodarilo sa zrušiť parent order {parent_order_id_local} po zlyhaní SL: {e_cancel_p_on_sl_fail}")
            return False

        logger.info(f"[{symbol}] SL order ID: {sl_order_id_final}, Stav: {sl_trade_submission_details.orderStatus.status if sl_trade_submission_details and sl_trade_submission_details.orderStatus else 'N/A'}")
        instrument_data['sl_order_id'] = sl_order_id_final
        instrument_data['sl_price'] = final_sl_price

    except Exception as e_bracket_main_exc:
        logger.error(f"[{symbol}] Všeobecná chyba v place_bracket_order: {e_bracket_main_exc}", exc_info=True)
        if parent_market_order_obj and parent_market_order_obj.orderId and parent_market_order_obj.orderId !=0 :
            try:
                logger.warning(f"[{symbol}] Pokus o zrušenie parent orderu {parent_market_order_obj.orderId} kvôli všeobecnej chybe v place_bracket.")
                ib_conn.cancelOrder(parent_market_order_obj)
                logger.info(f"[{symbol}] Parent order {parent_market_order_obj.orderId} zrušený po všeobecnej chybe.")
            except Exception as e_cancel_final_bracket_exc:
                logger.error(f"[{symbol}] Nepodarilo sa zrušiť parent order {parent_market_order_obj.orderId} po všeobecnej chybe: {e_cancel_final_bracket_exc}")
        return False

    instrument_data['trail_offset_reached'] = False
    if signal_type == 'LONG':
        instrument_data['trail_activation_price'] = ref_entry_price + trail_activation_price_difference
        instrument_data['trail_offset'] = trail_offset_as_price
    else:
        instrument_data['trail_activation_price'] = ref_entry_price - trail_activation_price_difference
        instrument_data['trail_offset'] = trail_offset_as_price

    price_format_msg_final = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
    msg_success_tg = (f"[{symbol}] Príkazy pre {signal_type} zadané. Ref_Entry @ {ref_entry_price:{price_format_msg_final}}. "
                      f"SL @ {final_sl_price:{price_format_msg_final}} (ID: {sl_order_id_final if sl_order_id_final else 'N/A'}). "
                      f"Trail aktivácia @ {instrument_data['trail_activation_price']:{price_format_msg_final}}")
    logger.info(msg_success_tg)
    send_telegram(msg_success_tg)

    return True