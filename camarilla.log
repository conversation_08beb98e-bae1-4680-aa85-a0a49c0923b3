2025-05-29 13:18:42 INFO:utils.py:33:utils:Logging nastavený na úroveň: DEBUG
2025-05-29 13:18:42 INFO:camarilla.py:278:__main__:<PERSON><PERSON><PERSON><PERSON> multi-inštrument bot, TF=1 hour, PID=689537
2025-05-29 13:18:42 INFO:camarilla.py:285:__main__:Inicializujem s dynamickým výberom najlepš<PERSON><PERSON> kontrak<PERSON>
2025-05-29 13:18:42 INFO:camarilla.py:294:__main__:Pokus o pripojenie k IB na 127.0.0.1:4001 s ClientID 1
2025-05-29 13:18:42 INFO:camarilla.py:299:__main__:Pokus o pripojenie č. 1/15...
2025-05-29 13:18:42 INFO:client.py:204:ib_insync.client:Connecting to 127.0.0.1:4001 with clientId 1...
2025-05-29 13:18:42 INFO:client.py:212:ib_insync.client:Connected
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 176,20250529 11:18:41 GMT
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 71,2,1,
2025-05-29 13:18:42 INFO:client.py:341:ib_insync.client:Logged on to server version 176
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 15,1,DUK870453
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 9,1,1436
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 4,2,-1,2104,Market data farm connection is OK:usfarm,
2025-05-29 13:18:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2104, reqId -1: Market data farm connection is OK:usfarm
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 4,2,-1,2107,HMDS data farm connection is inactive but should be available upon demand.ushmds,
2025-05-29 13:18:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2107, reqId -1: HMDS data farm connection is inactive but should be available upon demand.ushmds
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 4,2,-1,2158,Sec-def data farm connection is OK:secdefil,
2025-05-29 13:18:42 INFO:wrapper.py:1111:ib_insync.wrapper:Warning 2158, reqId -1: Sec-def data farm connection is OK:secdefil
2025-05-29 13:18:42 INFO:client.py:218:ib_insync.client:API connection ready
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 61,1
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 5,1
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 99,0
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 6,2,1,DUK870453
2025-05-29 13:18:42 DEBUG:client.py:285:ib_insync.client:>>> 76,1,1436,DUK870453,,0
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 61,3,DUK870453,*********,MES,FUT,********,0.0,,5,,USD,MESM5,MES,2,29693.12
2025-05-29 13:18:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, avgCost=29693.12)
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 61,3,DUK870453,6********,M2K,FUT,********,0.0,,5,,USD,M2KM5,M2K,2,10456.12
2025-05-29 13:18:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=6********, symbol='M2K', lastTradeDateOrContractMonth='********', multiplier='5', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, avgCost=10456.12)
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 61,3,DUK870453,*********,MNQ,FUT,********,0.0,,2,,USD,MNQM5,MNQ,0,0.0
2025-05-29 13:18:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', multiplier='2', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, avgCost=0.0)
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 61,3,DUK870453,*********,M6A,FUT,********,0.0,,10000,,USD,M6AM5,M6A,-1,6410.59
2025-05-29 13:18:42 INFO:wrapper.py:301:ib_insync.wrapper:position: Position(account='DUK870453', contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', multiplier='10000', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, avgCost=6410.59)
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 62,1
2025-05-29 13:18:42 DEBUG:client.py:327:ib_insync.client:<<< 53,1
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountCode,DUK870453,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountOrGroup,DUK870453,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountOrGroup,DUK870453,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountOrGroup,DUK870453,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountReady,true,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccountType,INDIVIDUAL,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedCash,1207.60,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedCash,1207.60,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedCash,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedCash-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedDividend,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AccruedDividend-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AvailableFunds,994674.15,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AvailableFunds-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Billable,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Billable-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,BuyingPower,6631160.98,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CashBalance,999943.382,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CashBalance,1000033.39,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CashBalance,-101.5893,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ColumnPrio-P,5,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ColumnPrio-S,1,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CorporateBondValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CorporateBondValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CorporateBondValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Cryptocurrency,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Cryptocurrency,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Cryptocurrency,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Currency,BASE,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Currency,EUR,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Currency,USD,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Cushion,0.994204,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,EquityWithLoanValue,1001150.98,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,EquityWithLoanValue-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExcessLiquidity,995348.23,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExcessLiquidity-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExchangeRate,1.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExchangeRate,1.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExchangeRate,0.8859989,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullAvailableFunds,994674.15,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullAvailableFunds-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullExcessLiquidity,995348.23,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullExcessLiquidity-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullInitMarginReq,6476.84,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullInitMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullMaintMarginReq,5802.76,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullMaintMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FundValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FundValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FundValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FutureOptionValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FutureOptionValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FutureOptionValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FuturesPNL,763.99,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FuturesPNL,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FuturesPNL,862.29,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FxCashBalance,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FxCashBalance,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FxCashBalance,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,GrossPositionValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,GrossPositionValue-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Guarantee,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Guarantee-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IncentiveCoupons,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IncentiveCoupons-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IndianStockHaircut,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IndianStockHaircut-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,InitMarginReq,6476.84,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,InitMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IssuerOptionValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IssuerOptionValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,IssuerOptionValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Leverage-P,0.00,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Leverage-S,0.00,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadAvailableFunds,996547.09,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadAvailableFunds-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadExcessLiquidity,997028.07,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadExcessLiquidity-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadInitMarginReq,4603.89,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadInitMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadMaintMarginReq,4122.91,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadMaintMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadNextChange,1748525400,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MaintMarginReq,5802.76,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MaintMarginReq-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MoneyMarketFundValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MoneyMarketFundValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MoneyMarketFundValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MutualFundValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MutualFundValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MutualFundValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NLVAndMarginInReview,false,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetDividend,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetDividend,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetDividend,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidation,1001150.98,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidation-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationByCurrency,1001150.982,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationByCurrency,1001240.99,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationByCurrency,-101.5893,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationUncertainty,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,OptionMarketValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,OptionMarketValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,OptionMarketValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PASharesValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PASharesValue-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PhysicalCertificateValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PhysicalCertificateValue-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PostExpirationExcess,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PostExpirationExcess-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PostExpirationMargin,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,PostExpirationMargin-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealCurrency,BASE,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealCurrency,EUR,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealCurrency,USD,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealizedPnL,-36.98,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealizedPnL,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealizedPnL,-41.74,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,SegmentTitle-P,Crypto at Paxos,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,SegmentTitle-S,CFD,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,StockMarketValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,StockMarketValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,StockMarketValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBillValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBillValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBillValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBondValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBondValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TBondValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashBalance,999943.382,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashBalance,1000033.39,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashBalance,-101.5893,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashValue,999943.38,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashValue-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalDebitCardPendingCharges,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalDebitCardPendingCharges-P,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TradingType-S,STKNOPT,,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,UnrealizedPnL,299.82,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,UnrealizedPnL,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,UnrealizedPnL,338.40,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,WarrantValue,0.00,BASE,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,WarrantValue,0.00,EUR,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 6,2,WarrantValue,0.00,USD,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 7,8,6********,M2K,FUT,********,0,0,5,CME,USD,M2KM5,M2K,2,2097.1569824,20971.57,10456.12,59.33,0.0,DUK870453
2025-05-29 13:18:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=6********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2097.1569824, marketValue=20971.57, averageCost=10456.12, unrealizedPNL=59.33, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:16
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,M6A,FUT,********,0,0,10000,CME,USD,M6AM5,M6A,-1,0.6440626,-6440.63,6410.59,-30.04,0.0,DUK870453
2025-05-29 13:18:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6440626, marketValue=-6440.63, averageCost=6410.59, unrealizedPNL=-30.04, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:16
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,MES,FUT,********,0,0,5,CME,USD,MESM5,MES,2,5969.********,59695.35,29693.12,309.11,0.0,DUK870453
2025-05-29 13:18:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5969.********, marketValue=59695.35, averageCost=29693.12, unrealizedPNL=309.11, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:16
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,MNQ,FUT,********,0,0,2,CME,USD,MNQM5,MNQ,0,21730.3847656,0.0,0.0,0.0,-41.74,DUK870453
2025-05-29 13:18:43 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21730.3847656, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:16
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:16
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 54,1,DUK870453
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountCode,DUK870453,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountReady,true,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountType,INDIVIDUAL,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,1207.60,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedDividend,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedDividend-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AvailableFunds,994674.15,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AvailableFunds-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Billable,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Billable-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,BuyingPower,6631160.98,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ColumnPrio-P,5,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ColumnPrio-S,1,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cushion,0.994204,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,EquityWithLoanValue,1001150.98,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,EquityWithLoanValue-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExcessLiquidity,995348.23,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExcessLiquidity-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullAvailableFunds,994674.15,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullAvailableFunds-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullExcessLiquidity,995348.23,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullExcessLiquidity-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullInitMarginReq,6476.84,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullInitMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullMaintMarginReq,5802.76,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullMaintMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,GrossPositionValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,GrossPositionValue-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Guarantee,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Guarantee-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IncentiveCoupons,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IncentiveCoupons-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IndianStockHaircut,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IndianStockHaircut-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,InitMarginReq,6476.84,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,InitMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Leverage-P,0.00,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Leverage-S,0.00,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadAvailableFunds,996547.09,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadAvailableFunds-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadExcessLiquidity,997028.07,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadExcessLiquidity-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadInitMarginReq,4603.89,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadInitMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadMaintMarginReq,4122.91,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadMaintMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadNextChange,1748525400,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MaintMarginReq,5802.76,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MaintMarginReq-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NLVAndMarginInReview,false,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidation,1001150.98,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidation-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationUncertainty,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PASharesValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PASharesValue-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PhysicalCertificateValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PhysicalCertificateValue-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PostExpirationExcess,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PostExpirationExcess-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PostExpirationMargin,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,PostExpirationMargin-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,SegmentTitle-P,Crypto at Paxos,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,SegmentTitle-S,CFD,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashValue,999943.38,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashValue-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalDebitCardPendingCharges,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalDebitCardPendingCharges-P,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TradingType-S,STKNOPT,
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Currency,EUR,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CashBalance,1000033.39,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashBalance,1000033.39,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,1207.60,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,StockMarketValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,OptionMarketValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FutureOptionValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FuturesPNL,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationByCurrency,1001240.99,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,UnrealizedPnL,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealizedPnL,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExchangeRate,1.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FundValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetDividend,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MutualFundValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MoneyMarketFundValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CorporateBondValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBondValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBillValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,WarrantValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FxCashBalance,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountOrGroup,DUK870453,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealCurrency,EUR,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IssuerOptionValue,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cryptocurrency,0.00,EUR
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Currency,USD,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CashBalance,-101.5893,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashBalance,-101.5893,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,StockMarketValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,OptionMarketValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FutureOptionValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FuturesPNL,862.29,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationByCurrency,-101.5893,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,UnrealizedPnL,338.40,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealizedPnL,-41.74,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExchangeRate,0.8859989,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FundValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetDividend,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MutualFundValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MoneyMarketFundValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CorporateBondValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBondValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBillValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,WarrantValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FxCashBalance,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountOrGroup,DUK870453,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealCurrency,USD,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IssuerOptionValue,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cryptocurrency,0.00,USD
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Currency,BASE,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CashBalance,999943.382,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashBalance,999943.382,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,1207.60,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,StockMarketValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,OptionMarketValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FutureOptionValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FuturesPNL,763.99,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationByCurrency,1001150.982,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,UnrealizedPnL,299.82,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealizedPnL,-36.98,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExchangeRate,1.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FundValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetDividend,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MutualFundValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MoneyMarketFundValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CorporateBondValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBondValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBillValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,WarrantValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FxCashBalance,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountOrGroup,DUK870453,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealCurrency,BASE,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IssuerOptionValue,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cryptocurrency,0.00,BASE
2025-05-29 13:18:43 DEBUG:client.py:327:ib_insync.client:<<< 74,1,1436
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,Cushion,0.994202,,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cushion,0.994202,
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,EquityWithLoanValue,1001156.02,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidation,1001156.02,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashValue,999948.42,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,BuyingPower,6631174.73,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,InitMarginReq,6479.81,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,MaintMarginReq,5804.95,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,AvailableFunds,994676.21,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExcessLiquidity,995351.06,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadInitMarginReq,4605.83,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadMaintMarginReq,4124.40,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadAvailableFunds,996550.19,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,LookAheadExcessLiquidity,997031.61,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullInitMarginReq,6479.81,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullMaintMarginReq,5804.95,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullAvailableFunds,994676.21,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FullExcessLiquidity,995351.06,EUR,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AvailableFunds,994676.21,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,BuyingPower,6631174.73,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,EquityWithLoanValue,1001156.02,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExcessLiquidity,995351.06,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullAvailableFunds,994676.21,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullExcessLiquidity,995351.06,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullInitMarginReq,6479.81,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FullMaintMarginReq,5804.95,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,InitMarginReq,6479.81,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadAvailableFunds,996550.19,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadExcessLiquidity,997031.61,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadInitMarginReq,4605.83,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,LookAheadMaintMarginReq,4124.40,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MaintMarginReq,5804.95,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidation,1001156.02,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashValue,999948.42,EUR
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CashBalance,-95.8686,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashBalance,-95.8686,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FuturesPNL,868.01,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationByCurrency,-95.8686,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,UnrealizedPnL,344.12,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,ExchangeRate,0.8863671,USD,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,CashBalance,999948.4152,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,TotalCashBalance,999948.4152,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,FuturesPNL,769.38,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,NetLiquidationByCurrency,1001156.0152,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,UnrealizedPnL,305.02,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 6,2,RealizedPnL,-37.00,BASE,DUK870453
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountOrGroup,DUK870453,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccountOrGroup,DUK870453,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,1207.60,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,AccruedCash,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CashBalance,999948.4152,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CashBalance,-95.8686,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CorporateBondValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,CorporateBondValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cryptocurrency,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Cryptocurrency,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Currency,BASE,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,Currency,USD,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExchangeRate,1.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,ExchangeRate,0.8863671,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FundValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FundValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FutureOptionValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FutureOptionValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FuturesPNL,769.38,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FuturesPNL,868.01,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FxCashBalance,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,FxCashBalance,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IssuerOptionValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,IssuerOptionValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MoneyMarketFundValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MoneyMarketFundValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MutualFundValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,MutualFundValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetDividend,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetDividend,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationByCurrency,1001156.0152,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,NetLiquidationByCurrency,-95.8686,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,OptionMarketValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,OptionMarketValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealCurrency,BASE,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealCurrency,USD,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealizedPnL,-37.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,RealizedPnL,-41.74,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,StockMarketValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,StockMarketValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBillValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBillValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBondValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TBondValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashBalance,999948.4152,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,TotalCashBalance,-95.8686,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,UnrealizedPnL,305.02,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,UnrealizedPnL,344.12,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,WarrantValue,0.00,BASE
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 73,1,1436,DUK870453,,WarrantValue,0.00,USD
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 7,8,6********,M2K,FUT,********,0,0,5,CME,USD,M2KM5,M2K,2,2096.********,20966.75,10456.12,54.51,0.0,DUK870453
2025-05-29 13:19:01 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=6********, symbol='M2K', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='M2KM5', tradingClass='M2K'), position=2.0, marketPrice=2096.********, marketValue=20966.75, averageCost=10456.12, unrealizedPNL=54.51, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,MES,FUT,********,0,0,5,CME,USD,MESM5,MES,2,5970.3261719,59703.26,29693.12,317.02,0.0,DUK870453
2025-05-29 13:19:01 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MES', lastTradeDateOrContractMonth='********', right='0', multiplier='5', primaryExchange='CME', currency='USD', localSymbol='MESM5', tradingClass='MES'), position=2.0, marketPrice=5970.3261719, marketValue=59703.26, averageCost=29693.12, unrealizedPNL=317.02, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,MNQ,FUT,********,0,0,2,CME,USD,MNQM5,MNQ,0,21732.9589844,0.0,0.0,0.0,-41.74,DUK870453
2025-05-29 13:19:01 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='MNQ', lastTradeDateOrContractMonth='********', right='0', multiplier='2', primaryExchange='CME', currency='USD', localSymbol='MNQM5', tradingClass='MNQ'), position=0.0, marketPrice=21732.9589844, marketValue=0.0, averageCost=0.0, unrealizedPNL=0.0, realizedPNL=-41.74, account='DUK870453')
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 7,8,*********,M6A,FUT,********,0,0,10000,CME,USD,M6AM5,M6A,-1,0.6438001,-6438.0,6410.59,-27.41,0.0,DUK870453
2025-05-29 13:19:01 INFO:wrapper.py:288:ib_insync.wrapper:updatePortfolio: PortfolioItem(contract=Future(conId=*********, symbol='M6A', lastTradeDateOrContractMonth='********', right='0', multiplier='10000', primaryExchange='CME', currency='USD', localSymbol='M6AM5', tradingClass='M6A'), position=-1.0, marketPrice=0.6438001, marketValue=-6438.0, averageCost=6410.59, unrealizedPNL=-27.41, realizedPNL=0.0, account='DUK870453')
2025-05-29 13:19:01 DEBUG:client.py:327:ib_insync.client:<<< 8,1,11:19
2025-05-29 13:19:02 ERROR:ib.py:1779:ib_insync.ib:completed orders request timed out
2025-05-29 13:19:02 DEBUG:client.py:285:ib_insync.client:>>> 7,3,1437,0,,,,,,
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 11,1437,1429,*********,MNQ,FUT,********,0.0,,2,CME,USD,MNQM5,MNQ,0000e1a7.6837961d.01.01,******** 19:00:03 US/Central,DUK870453,CME,BOT,1,21768.75,**********,1,0,1,21768.75,,,,,1
2025-05-29 13:19:02 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.6837961d.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 3, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='BOT', shares=1.0, price=21768.75, permId=**********, clientId=1, orderId=1429, liquidation=0, cumQty=1.0, avgPrice=21768.75, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 11,1437,1430,*********,MNQ,FUT,********,0.0,,2,CME,USD,MNQM5,MNQ,0000e1a7.68379656.01.01,******** 19:00:44 US/Central,DUK870453,CME,SLD,1,21748.50,1888977602,1,0,1,21748.50,,,,,1
2025-05-29 13:19:02 INFO:wrapper.py:459:ib_insync.wrapper:execDetails Execution(execId='0000e1a7.68379656.01.01', time=datetime.datetime(2025, 5, 29, 0, 0, 44, tzinfo=datetime.timezone.utc), acctNumber='DUK870453', exchange='CME', side='SLD', shares=1.0, price=21748.5, permId=1888977602, clientId=1, orderId=1430, liquidation=0, cumQty=1.0, avgPrice=21748.5, orderRef='', evRule='', evMultiplier=0.0, modelCode='', lastLiquidity=1)
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 59,1,0000e1a7.6837961d.01.01,0.62,USD,1.7976931348623157E308,1.7976931348623157E308,
2025-05-29 13:19:02 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.6837961d.01.01', commission=0.62, currency='USD', realizedPNL=0.0, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 59,1,0000e1a7.68379656.01.01,0.62,USD,-41.74,1.7976931348623157E308,
2025-05-29 13:19:02 INFO:wrapper.py:504:ib_insync.wrapper:commissionReport: CommissionReport(execId='0000e1a7.68379656.01.01', commission=0.62, currency='USD', realizedPNL=-41.74, yield_=0.0, yieldRedemptionDate=0)
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 55,1,1437
2025-05-29 13:19:02 INFO:ib.py:1789:ib_insync.ib:Synchronization complete
2025-05-29 13:19:02 INFO:camarilla.py:302:__main__:Úspešne pripojený k IB.
2025-05-29 13:19:02 INFO:camarilla.py:304:__main__:Volám initialize_instruments_data() z __main__ po pripojení.
2025-05-29 13:19:02 INFO:camarilla.py:29:__main__:Inicializujem/Reinicializujem dáta inštrumentov...
2025-05-29 13:19:02 INFO:camarilla.py:64:__main__:[M2K] Načítavam najlepší dostupný kontrakt...
2025-05-29 13:19:02 INFO:utils.py:133:utils:[M2K] Hľadám najlepší kontrakt na CME...
2025-05-29 13:19:02 DEBUG:client.py:285:ib_insync.client:>>> 9,8,1438,0,M2K,FUT,,0.0,,,CME,,USD,,,0,,,
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 10,1438,M2K,FUT,******** 08:30:00 US/Central,0,,CME,USD,M2KM5,M2K,M2K,6********,0.1,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687595,Micro E-Mini Russell 2000 Index,,202506,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,M2K,IND,28,********,,1,1,1
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 10,1438,M2K,FUT,20250919 08:30:00 US/Central,0,,CME,USD,M2KU5,M2K,M2K,711280062,0.1,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687595,Micro E-Mini Russell 2000 Index,,202509,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,M2K,IND,28,20250919,,1,1,1
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 10,1438,M2K,FUT,20251219 08:30:00 US/Central,0,,CME,USD,M2KZ5,M2K,M2K,730283080,0.1,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687595,Micro E-Mini Russell 2000 Index,,202512,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,M2K,IND,28,20251219,,1,1,1
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 10,1438,M2K,FUT,20260320 08:30:00 US/Central,0,,CME,USD,M2KH6,M2K,M2K,750150181,0.1,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687595,Micro E-Mini Russell 2000 Index,,202603,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,M2K,IND,28,20260320,,1,1,1
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 10,1438,M2K,FUT,20260618 08:30:00 US/Central,0,,CME,USD,M2KM6,M2K,M2K,770561189,0.1,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687595,Micro E-Mini Russell 2000 Index,,202606,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,M2K,IND,28,20260618,,1,1,1
2025-05-29 13:19:02 DEBUG:client.py:327:ib_insync.client:<<< 52,1,1438
2025-05-29 13:19:02 DEBUG:utils.py:145:utils:[M2K] Našiel som 5 kontraktov
2025-05-29 13:19:02 DEBUG:utils.py:189:utils:[M2K] Kandidát: M2KM5 - expiruje za 21 dní
2025-05-29 13:19:02 DEBUG:utils.py:189:utils:[M2K] Kandidát: M2KU5 - expiruje za 112 dní
2025-05-29 13:19:02 DEBUG:utils.py:189:utils:[M2K] Kandidát: M2KZ5 - expiruje za 203 dní
2025-05-29 13:19:02 DEBUG:utils.py:189:utils:[M2K] Kandidát: M2KH6 - expiruje za 294 dní
2025-05-29 13:19:02 DEBUG:utils.py:179:utils:[M2K] Preskakujem M2KM6 - expiruje príliš ďaleko (384 dní)
2025-05-29 13:19:02 INFO:utils.py:205:utils:[M2K] Vybraný najlepší kontrakt: M2KH6 (expiruje za 294 dní - 2026-03-20)
2025-05-29 13:19:02 INFO:camarilla.py:70:__main__:[M2K] Kontrakt úspešne načítaný: M2KH6
2025-05-29 13:19:02 INFO:camarilla.py:64:__main__:[MES] Načítavam najlepší dostupný kontrakt...
2025-05-29 13:19:02 INFO:utils.py:133:utils:[MES] Hľadám najlepší kontrakt na CME...
2025-05-29 13:19:02 DEBUG:client.py:285:ib_insync.client:>>> 9,8,1439,0,MES,FUT,,0.0,,,CME,,USD,,,0,,,
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1439,MES,FUT,******** 08:30:00 US/Central,0,,CME,USD,MESM5,MES,MES,*********,0.25,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362673777,Micro E-Mini S&P 500 Stock Price Index,,202506,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MES,IND,67,********,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1439,MES,FUT,20250919 08:30:00 US/Central,0,,CME,USD,MESU5,MES,MES,711280067,0.25,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362673777,Micro E-Mini S&P 500 Stock Price Index,,202509,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MES,IND,67,20250919,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1439,MES,FUT,20251219 08:30:00 US/Central,0,,CME,USD,MESZ5,MES,MES,730283085,0.25,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362673777,Micro E-Mini S&P 500 Stock Price Index,,202512,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MES,IND,67,20251219,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1439,MES,FUT,20260320 08:30:00 US/Central,0,,CME,USD,MESH6,MES,MES,750150186,0.25,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362673777,Micro E-Mini S&P 500 Stock Price Index,,202603,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MES,IND,67,20260320,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1439,MES,FUT,20260618 08:30:00 US/Central,0,,CME,USD,MESM6,MES,MES,770561194,0.25,5,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362673777,Micro E-Mini S&P 500 Stock Price Index,,202606,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MES,IND,67,20260618,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 52,1,1439
2025-05-29 13:19:03 DEBUG:utils.py:145:utils:[MES] Našiel som 5 kontraktov
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MES] Kandidát: MESM5 - expiruje za 21 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MES] Kandidát: MESU5 - expiruje za 112 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MES] Kandidát: MESZ5 - expiruje za 203 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MES] Kandidát: MESH6 - expiruje za 294 dní
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MES] Preskakujem MESM6 - expiruje príliš ďaleko (384 dní)
2025-05-29 13:19:03 INFO:utils.py:205:utils:[MES] Vybraný najlepší kontrakt: MESH6 (expiruje za 294 dní - 2026-03-20)
2025-05-29 13:19:03 INFO:camarilla.py:70:__main__:[MES] Kontrakt úspešne načítaný: MESH6
2025-05-29 13:19:03 INFO:camarilla.py:64:__main__:[MNQ] Načítavam najlepší dostupný kontrakt...
2025-05-29 13:19:03 INFO:utils.py:133:utils:[MNQ] Hľadám najlepší kontrakt na CME...
2025-05-29 13:19:03 DEBUG:client.py:285:ib_insync.client:>>> 9,8,1440,0,MNQ,FUT,,0.0,,,CME,,USD,,,0,,,
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1440,MNQ,FUT,******** 08:30:00 US/Central,0,,CME,USD,MNQM5,MNQ,MNQ,*********,0.25,2,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687422,Micro E-Mini Nasdaq-100 Index,,202506,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MNQ,IND,67,********,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1440,MNQ,FUT,20250919 08:30:00 US/Central,0,,CME,USD,MNQU5,MNQ,MNQ,711280073,0.25,2,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687422,Micro E-Mini Nasdaq-100 Index,,202509,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MNQ,IND,67,20250919,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1440,MNQ,FUT,20251219 08:30:00 US/Central,0,,CME,USD,MNQZ5,MNQ,MNQ,730283094,0.25,2,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687422,Micro E-Mini Nasdaq-100 Index,,202512,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MNQ,IND,67,20251219,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1440,MNQ,FUT,20260320 08:30:00 US/Central,0,,CME,USD,MNQH6,MNQ,MNQ,750150193,0.25,2,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687422,Micro E-Mini Nasdaq-100 Index,,202603,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MNQ,IND,67,20260320,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1440,MNQ,FUT,20260618 08:30:00 US/Central,0,,CME,USD,MNQM6,MNQ,MNQ,770561201,0.25,2,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,CME,1,362687422,Micro E-Mini Nasdaq-100 Index,,202606,,,,US/Central,20250527:1700-********:1600;********:1700-20250529:1600;20250529:1700-20250530:1600;20250531:CLOSED;20250601:1700-20250602:1600;20250602:1700-20250603:1600,********:0830-********:1600;20250529:0830-20250529:1600;20250530:0830-20250530:1600;20250531:CLOSED;20250601:CLOSED;20250602:0830-20250602:1600;20250602:1700-20250603:1600,,,0,2147483647,MNQ,IND,67,20260618,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 52,1,1440
2025-05-29 13:19:03 DEBUG:utils.py:145:utils:[MNQ] Našiel som 5 kontraktov
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MNQ] Kandidát: MNQM5 - expiruje za 21 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MNQ] Kandidát: MNQU5 - expiruje za 112 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MNQ] Kandidát: MNQZ5 - expiruje za 203 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MNQ] Kandidát: MNQH6 - expiruje za 294 dní
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MNQ] Preskakujem MNQM6 - expiruje príliš ďaleko (384 dní)
2025-05-29 13:19:03 INFO:utils.py:205:utils:[MNQ] Vybraný najlepší kontrakt: MNQH6 (expiruje za 294 dní - 2026-03-20)
2025-05-29 13:19:03 INFO:camarilla.py:70:__main__:[MNQ] Kontrakt úspešne načítaný: MNQH6
2025-05-29 13:19:03 INFO:camarilla.py:64:__main__:[MGC] Načítavam najlepší dostupný kontrakt...
2025-05-29 13:19:03 INFO:utils.py:133:utils:[MGC] Hľadám najlepší kontrakt na COMEX...
2025-05-29 13:19:03 DEBUG:client.py:285:ib_insync.client:>>> 9,8,1441,0,MGC,FUT,,0.0,,,COMEX,,USD,,,0,,,
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20250626 13:30:00 US/Eastern,0,,COMEX,USD,MGCM5,MGC,MGC,639786536,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202506,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20250626,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20250827 13:30:00 US/Eastern,0,,COMEX,USD,MGCQ5,MGC,MGC,656780482,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202508,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20250827,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20251029 13:30:00 US/Eastern,0,,COMEX,USD,MGCV5,MGC,MGC,668631622,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202510,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20251029,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20251229 13:30:00 US/Eastern,0,,COMEX,USD,MGCZ5,MGC,MGC,674701641,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202512,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20251229,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20260225 13:30:00 US/Eastern,0,,COMEX,USD,MGCG6,MGC,MGC,693609542,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202602,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20260225,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20260428 13:30:00 US/Eastern,0,,COMEX,USD,MGCJ6,MGC,MGC,706903676,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202604,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20260428,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20260626 13:30:00 US/Eastern,0,,COMEX,USD,MGCM6,MGC,MGC,712565978,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202606,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20260626,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20260827 13:30:00 US/Eastern,0,,COMEX,USD,MGCQ6,MGC,MGC,732156883,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202608,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20260827,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20261028 13:30:00 US/Eastern,0,,COMEX,USD,MGCV6,MGC,MGC,744880158,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202610,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20261028,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20261229 13:30:00 US/Eastern,0,,COMEX,USD,MGCZ6,MGC,MGC,751494403,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202612,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20261229,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20270224 13:30:00 US/Eastern,0,,COMEX,USD,MGCG7,MGC,MGC,772435617,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202702,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20270224,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 10,1441,MGC,FUT,20270428 13:30:00 US/Eastern,0,,COMEX,USD,MGCJ7,MGC,MGC,788288493,0.1,10,ACTIVETIM,AD,ADJUST,ALERT,ALGO,ALLOC,AVGCOST,BASKET,BENCHPX,COND,CONDORDER,DAY,DEACT,DEACTDIS,DEACTEOD,GAT,GTC,GTD,GTT,HID,ICE,IOC,LIT,LMT,LTH,MIT,MKT,MTL,NGCOMB,NONALGO,OCA,PEGBENCH,SCALE,SCALERST,SIZECHK,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF,COMEX,1,79702479,E-Micro Gold ,,202704,,,,US/Eastern,20250527:1800-********:1700;********:1800-20250529:1700;20250529:1800-20250530:1700;20250531:CLOSED;20250601:1800-20250602:1700;20250602:1800-20250603:1700,********:0930-********:1700;20250529:0930-20250529:1700;20250530:0930-20250530:1700;20250531:CLOSED;20250601:CLOSED;20250602:0930-20250602:1700;20250602:1800-20250603:1700,,,0,2147483647,MGC,IND,65,20270428,,1,1,1
2025-05-29 13:19:03 DEBUG:client.py:327:ib_insync.client:<<< 52,1,1441
2025-05-29 13:19:03 DEBUG:utils.py:145:utils:[MGC] Našiel som 12 kontraktov
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCM5 - expiruje za 27 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCQ5 - expiruje za 89 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCV5 - expiruje za 152 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCZ5 - expiruje za 213 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCG6 - expiruje za 271 dní
2025-05-29 13:19:03 DEBUG:utils.py:189:utils:[MGC] Kandidát: MGCJ6 - expiruje za 333 dní
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCM6 - expiruje príliš ďaleko (392 dní)
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCQ6 - expiruje príliš ďaleko (454 dní)
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCV6 - expiruje príliš ďaleko (516 dní)
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCZ6 - expiruje príliš ďaleko (578 dní)
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCG7 - expiruje príliš ďaleko (635 dní)
2025-05-29 13:19:03 DEBUG:utils.py:179:utils:[MGC] Preskakujem MGCJ7 - expiruje príliš ďaleko (698 dní)
2025-05-29 13:19:03 INFO:utils.py:205:utils:[MGC] Vybraný najlepší kontrakt: MGCJ6 (expiruje za 333 dní - 2026-04-28)
2025-05-29 13:19:03 INFO:camarilla.py:70:__main__:[MGC] Kontrakt úspešne načítaný: MGCJ6
2025-05-29 13:19:03 INFO:camarilla.py:83:__main__:Inicializovaných/Reinicializovaných 4 inštrumentov. Platných kontraktov: 4
2025-05-29 13:19:03 INFO:camarilla.py:165:__main__:run_main_trading_loop: Prvá barová iterácia, čakám na prvú celú sviečku...
2025-05-29 13:19:03 INFO:utils.py:330:utils:Čakám 2456.28s do ďalšej sviečky o 2025-05-29 14:00:00 CEST (čas servera).
