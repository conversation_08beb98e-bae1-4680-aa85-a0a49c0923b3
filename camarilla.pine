strategy(title="CamarillaStrategyV1", shorttitle="CD_Camarilla_StrategyV1", overlay=true) 

EMA = ema(close,8)

//Camarilla
h4 = close + (high - low) * 1.1 / 2.0
l4 = close - (high - low) * 1.1 / 2.0

//Daily Pivots 
dtime_h4 = security(tickerid, 'D', h4[1]) 
dtime_l4 = security(tickerid, 'D', l4[1]) 

longCondition = close >dtime_h4 and open < dtime_h4 and EMA < close
if (longCondition)
    strategy.entry("Long", strategy.long)
    strategy.exit ("Exit Long","Long",  trail_points = 40,trail_offset = 1, loss =70) 
    //strategy.exit ("Exit Long","Long",  trail_points = 40, loss =70) 
    //trail_points = 40, trail_offset = 3, loss =70 and


shortCondition = close <dtime_l4 and open >dtime_l4 and EMA > close
if (shortCondition)
    strategy.entry("Short", strategy.short)
    strategy.exit ("Exit Short","Short", trail_points = 10,trail_offset = 1, loss =20)
    //strategy.exit ("Exit Short","Short", trail_points = 40,trail_offset = 1, loss =70)
    
